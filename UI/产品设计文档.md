# 微信小程序打卡系统产品设计文档

## 1. 产品概述

### 1.1 产品定位
一款专为俱乐部小组管理设计的微信小程序，支持成员每日打卡、图片上传、打卡统计及自动提醒功能。

### 1.2 目标用户
- **管理员**：俱乐部负责人、活动组织者
- **普通成员**：参与打卡活动的俱乐部成员

### 1.3 核心价值
- 提高俱乐部成员参与度和活跃度
- 自动化管理和统计，减少人工工作量
- 通过打卡机制培养成员良好习惯

## 2. 功能架构

### 2.1 用户系统
- **微信授权登录**：一键获取用户基本信息（昵称、头像）
- **自动注册**：首次登录自动创建账户
- **个人中心**：展示用户基础信息和打卡数据

### 2.2 小组管理系统
- **创建小组**：管理员发布活动创建小组
  - 设置小组名称、描述
  - 设定每月最低打卡次数（默认22次）
- **成员管理**：
  - 二维码/小组ID邀请加入
  - 管理员移除成员权限
  - 成员主动退出功能
- **小组信息展示**：
  - 成员列表（头像、昵称、打卡状态）
  - 本月打卡要求及进度展示

### 2.3 打卡功能系统
- **每日打卡限制**：每人每天限打卡1次（00:00-23:59）
- **打卡内容**：
  - 必填：上传1张打卡图片（拍照/相册选择）
  - 选填：文字备注（限200字）
- **打卡记录**：
  - 日历视图展示个人打卡记录
  - 已打卡日期高亮显示
  - 点击日期查看详情（图片+备注）
- **数据验证**：
  - 防重复提交机制
  - 图片格式验证（jpg/png，≤5MB）

### 2.4 统计与提醒系统
- **月度统计**（每月1日自动生成）：
  - 成员打卡次数排行榜
  - 未达标成员标注（红色提醒）
  - 小组打卡完成率图表
- **自动提醒机制**：
  - 每月3日前@未达标成员
  - 每日20:00向未打卡成员推送通知
- **数据导出**：管理员可导出月度打卡数据

## 3. 技术规格

### 3.1 开发框架
- **平台**：微信小程序原生开发
- **语言**：TypeScript
- **数据**：前端使用Mock数据展示

### 3.2 功能特性
- 响应式设计，适配不同屏幕尺寸
- 离线缓存机制
- 图片压缩和上传优化
- 数据本地存储

## 4. 页面架构设计

### 4.1 核心页面

#### 首页（Home）
用途：用户进入小程序的主要入口，展示用户当前状态和快速操作
核心功能：
- 显示今日打卡状态
- 快速打卡入口
- 我的小组列表
- 个人打卡统计概览

#### 打卡页面（CheckIn）
用途：用户进行每日打卡的核心页面
核心功能：
- 拍照/选择图片上传
- 添加文字备注
- 提交打卡记录
- 显示打卡历史

#### 小组列表页面（GroupList）
用途：展示用户加入的所有小组
核心功能：
- 显示小组列表
- 小组基本信息展示
- 加入新小组入口
- 创建小组入口（管理员）

#### 小组详情页面（GroupDetail）
用途：展示特定小组的详细信息和成员状态
核心功能：
- 小组基本信息
- 成员列表及打卡状态
- 本月打卡统计
- 小组管理功能（管理员）

#### 创建小组页面（CreateGroup）
用途：管理员创建新的打卡小组
核心功能：
- 设置小组名称和描述
- 设定每月最低打卡次数
- 生成邀请二维码
- 小组规则设置

### 4.2 辅助页面

#### 加入小组页面（JoinGroup）
用途：用户通过邀请码或扫码加入小组
核心功能：
- 输入小组ID或扫描二维码
- 显示小组信息预览
- 确认加入小组

#### 打卡记录页面（CheckInHistory）
用途：展示用户的打卡历史记录
核心功能：
- 日历视图展示打卡记录
- 查看具体日期的打卡详情
- 打卡统计数据
- 月度打卡完成情况

#### 统计页面（Statistics）
用途：展示详细的打卡统计数据和排行榜
核心功能：
- 个人打卡统计图表
- 小组排行榜
- 月度完成率
- 历史数据对比

#### 个人中心页面（Profile）
用途：用户个人信息管理和设置
核心功能：
- 用户基本信息展示
- 打卡成就展示
- 设置和偏好
- 退出登录

#### 消息通知页面（Notifications）
用途：展示系统通知和提醒信息
核心功能：
- 打卡提醒通知
- 小组活动通知
- 系统消息
- 通知设置

## 5. 用户体验设计

### 5.1 设计原则
- 简洁直观的操作流程
- 清晰的信息层级
- 友好的反馈机制
- 符合微信设计规范

### 5.2 交互特点
- 一键打卡，操作简单
- 可视化数据展示
- 及时的状态反馈
- 智能提醒机制
