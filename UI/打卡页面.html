<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>打卡页面 - 打卡小程序</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { overflow: hidden; }
        ::-webkit-scrollbar { display: none; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card-shadow { box-shadow: 0 4px 20px rgba(0,0,0,0.08); }
        .upload-area { 
            background: linear-gradient(45deg, #f8fafc 25%, transparent 25%), 
                        linear-gradient(-45deg, #f8fafc 25%, transparent 25%), 
                        linear-gradient(45deg, transparent 75%, #f8fafc 75%), 
                        linear-gradient(-45deg, transparent 75%, #f8fafc 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 手机模拟器外框 -->
    <div class="mx-auto bg-black rounded-3xl p-2 shadow-2xl" style="width: 375px; height: 812px;">
        <div class="w-full h-full bg-white rounded-2xl overflow-hidden relative">
            <!-- 状态栏 -->
            <div class="flex justify-between items-center px-6 pt-3 pb-1 bg-transparent text-black text-sm font-medium">
                <span>9:41</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-xs"></i>
                    <i class="fas fa-wifi text-xs"></i>
                    <i class="fas fa-battery-three-quarters text-xs"></i>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="flex items-center justify-between px-6 py-4 bg-transparent">
                <button class="w-8 h-8 flex items-center justify-center">
                    <i class="fas fa-arrow-left text-gray-600"></i>
                </button>
                <h1 class="text-lg font-semibold text-gray-800">今日打卡</h1>
                <div class="w-8"></div>
            </div>

            <!-- 主要内容区域 -->
            <div class="px-6 pb-20 h-full">
                <!-- 日期信息 -->
                <div class="text-center mb-6">
                    <div class="text-3xl font-bold text-gray-800 mb-1">28</div>
                    <div class="text-sm text-gray-500">2024年6月 · 星期五</div>
                </div>

                <!-- 上传图片区域 -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-3">上传打卡图片 *</label>
                    <div class="upload-area border-2 border-dashed border-gray-300 rounded-xl p-8 text-center bg-gray-50">
                        <div class="mb-4">
                            <i class="fas fa-camera text-4xl text-gray-400"></i>
                        </div>
                        <p class="text-gray-500 mb-2">点击上传图片</p>
                        <p class="text-xs text-gray-400">支持 JPG、PNG 格式，大小不超过 5MB</p>
                        <div class="flex justify-center space-x-4 mt-4">
                            <button class="flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg text-sm">
                                <i class="fas fa-camera mr-2"></i>拍照
                            </button>
                            <button class="flex items-center px-4 py-2 bg-gray-500 text-white rounded-lg text-sm">
                                <i class="fas fa-image mr-2"></i>相册
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 文字备注 -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-3">添加备注</label>
                    <textarea 
                        placeholder="分享今天的心情或感受..."
                        class="w-full h-24 p-4 border border-gray-200 rounded-xl resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    ></textarea>
                    <div class="text-right text-xs text-gray-400 mt-1">0/200</div>
                </div>

                <!-- 选择小组 -->
                <div class="mb-8">
                    <label class="block text-sm font-medium text-gray-700 mb-3">选择小组</label>
                    <div class="space-y-2">
                        <div class="flex items-center p-3 bg-white rounded-xl border border-blue-200 card-shadow">
                            <input type="radio" name="group" class="mr-3 text-blue-500" checked>
                            <div class="flex items-center flex-1">
                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-running text-blue-500 text-sm"></i>
                                </div>
                                <span class="font-medium text-gray-800">晨跑俱乐部</span>
                            </div>
                        </div>
                        <div class="flex items-center p-3 bg-white rounded-xl border border-gray-200 card-shadow">
                            <input type="radio" name="group" class="mr-3 text-blue-500">
                            <div class="flex items-center flex-1">
                                <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-book text-purple-500 text-sm"></i>
                                </div>
                                <span class="font-medium text-gray-800">读书分享会</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 提交按钮 -->
                <button class="w-full gradient-bg text-white py-4 rounded-xl font-semibold text-lg shadow-lg">
                    完成打卡
                </button>
            </div>

            <!-- 底部导航栏 -->
            <div class="absolute bottom-0 left-0 right-0 bg-white border-t border-gray-100 px-6 py-3">
                <div class="flex justify-around items-center">
                    <div class="flex flex-col items-center">
                        <i class="fas fa-home text-gray-400 text-lg mb-1"></i>
                        <span class="text-xs text-gray-400">首页</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <i class="fas fa-users text-gray-400 text-lg mb-1"></i>
                        <span class="text-xs text-gray-400">小组</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <i class="fas fa-chart-bar text-gray-400 text-lg mb-1"></i>
                        <span class="text-xs text-gray-400">统计</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <i class="fas fa-user text-gray-400 text-lg mb-1"></i>
                        <span class="text-xs text-gray-400">我的</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
