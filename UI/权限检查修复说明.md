# 权限检查修复说明

## 问题描述
在首页切换时未查询当前用户角色，导致权限状态不能实时更新。

## 问题原因
在 `miniprogram/pages/home/<USER>

## 修复内容

### 1. 修复首页重复的 onShow 方法
**文件**: `miniprogram/pages/home/<USER>

**问题代码**:
```javascript
// 第一个 onShow 方法（包含权限检查）
onShow() {
  console.log('首页显示')
  this.checkUserRole()
  this.checkTodayStatus()
},

// ... 其他代码 ...

// 第二个 onShow 方法（覆盖了第一个）
onShow() {
  this.checkTodayStatus()
}
```

**修复后**:
```javascript
// 只保留包含权限检查的 onShow 方法
onShow() {
  console.log('首页显示')
  this.checkUserRole()
  this.checkTodayStatus()
},
```

### 2. 完善统计页面的权限检查
**文件**: `miniprogram/pages/statistics/statistics.js`

**添加内容**:
```javascript
// 导入权限管理模块
const { authManager, USER_ROLES } = require('../../utils/auth.js')

// 在 data 中添加 userRole 字段
data: {
  // ... 其他数据
  userRole: USER_ROLES.GUEST
},

// 添加权限检查方法
async checkUserRole() {
  try {
    await authManager.refreshUserInfo()
    const userRole = authManager.getCurrentRole()
    this.setData({ userRole })
  } catch (error) {
    console.error('获取用户角色失败:', error)
    this.setData({ userRole: USER_ROLES.GUEST })
  }
},

// 在 onLoad 和 onShow 中调用权限检查
onLoad() {
  this.checkUserRole()
  this.loadStatistics()
},

onShow() {
  this.checkUserRole()
  this.loadStatistics()
},
```

## 修复验证

### 测试步骤
1. **启动小程序**
   - 观察首页是否正确显示用户角色
   - 检查控制台是否有权限检查日志

2. **切换页面测试**
   - 从首页切换到其他页面
   - 再切换回首页
   - 观察是否重新查询用户角色

3. **权限变化测试**
   - 在个人中心修改个人信息
   - 切换到首页查看权限是否更新
   - 检查按钮显示状态是否正确

4. **统计页面测试**
   - 切换到统计页面
   - 观察是否正确获取用户角色
   - 检查控制台日志

### 预期结果
- ✅ 每次进入首页都会查询用户角色
- ✅ 权限状态实时更新
- ✅ 按钮显示状态正确
- ✅ 统计页面也能正确获取用户角色

## 相关页面权限检查状态

| 页面 | onLoad权限检查 | onShow权限检查 | 状态 |
|------|---------------|---------------|------|
| 首页 (home) | ✅ | ✅ | 已修复 |
| 小组 (groups) | ✅ | ✅ | 正常 |
| 个人中心 (profile) | ✅ | ✅ | 正常 |
| 统计 (statistics) | ✅ | ✅ | 已完善 |
| 打卡 (checkin) | ✅ | ❌ | 仅在进入时检查 |
| 创建小组 (create-group) | ✅ | ❌ | 仅在进入时检查 |
| 加入小组 (join-group) | ✅ | ❌ | 仅在进入时检查 |

## 注意事项

1. **Tab页面需要onShow检查**
   - 首页、小组、统计、个人中心是Tab页面
   - 用户可以直接切换，需要在onShow中检查权限

2. **功能页面只需onLoad检查**
   - 打卡、创建小组、加入小组是功能页面
   - 用户只能通过导航进入，在onLoad时检查即可

3. **权限检查的时机**
   - onLoad: 页面首次加载时
   - onShow: 页面显示时（包括从其他页面返回）

4. **错误处理**
   - 权限检查失败时设置为GUEST角色
   - 避免因网络问题导致功能异常

## 后续建议

1. **统一权限检查**
   - 考虑在页面基类中实现统一的权限检查逻辑
   - 减少重复代码

2. **权限缓存优化**
   - 实现权限信息的本地缓存
   - 减少不必要的API请求

3. **权限变化监听**
   - 实现全局的权限变化事件
   - 自动更新所有页面的权限状态
