// app.js
const { authManager } = require('./utils/auth.js')

App({
  globalData: {
    authManager: authManager
  },

  onLaunch() {
    console.log('小程序启动')

    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 初始化认证管理器
    authManager.init()

    // 自动登录
    this.autoLogin()
  },

  /**
   * 自动登录
   */
  async autoLogin() {
    try {
      // 如果没有token，执行微信登录
      const loginResult = await authManager.wxLogin()
      if (loginResult.success) {
        console.log('自动登录成功:', loginResult)
      } else {
        console.log('自动登录失败:', loginResult.error)
        // 登录失败时可以显示提示，但不阻塞应用启动
        wx.showToast({
          title: '登录失败，部分功能受限',
          icon: 'none',
          duration: 2000
        })
      }
    } catch (error) {
      console.error('自动登录过程出错:', error)
      // 出错时清除可能损坏的认证信息
      authManager.logout()
    }
  },

  /**
   * 手动重新登录
   */
  async reLogin() {
    try {
      wx.showLoading({
        title: '登录中...'
      })

      const loginResult = await authManager.wxLogin()
      wx.hideLoading()

      if (loginResult.success) {
        wx.showToast({
          title: '登录成功',
          icon: 'success'
        })
        return loginResult
      } else {
        wx.showToast({
          title: '登录失败',
          icon: 'none'
        })
        return loginResult
      }
    } catch (error) {
      wx.hideLoading()
      wx.showToast({
        title: '登录失败',
        icon: 'none'
      })
      return { success: false, error }
    }
  }
})

!function () {
  //获取页面配置并进行页面分享配置
  var PageTmp = Page
  Page = function (pageConfig) {
    pageConfig = Object.assign({
      onShareAppMessage() {
        return {
          title: "加入接龙学习打卡小程序，一起学习吧~",
          imageUrl: "https://minio.spaceink.top/community-hub/2025-09-13/default/1757726741847-bmqplw-9IrY2a.jpeg"
        }
      },
      onShareTimeline() {
        return {
          title: "加入接龙学习打卡小程序，一起学习吧~",
          imageUrl: "https://minio.spaceink.top/community-hub/2025-09-13/default/1757726741847-bmqplw-9IrY2a.jpeg"
        }
      }
    }, pageConfig);
    // 配置页面模板
    PageTmp(pageConfig);
  }
}();