// base.js - 基础配置文件
// 存放所有环境共用的配置项
module.exports = {
  // OSS前缀URL
  ossPrefixUrl: '',
  
  // 默认头像
  defaultAvatarUrl: 'https://minio.spaceink.top/community-hub/2025-07-13/default/1752408116245-49e807-4wqf2A.jpeg',
  
  // 请求超时时间
  requestTimeout: 10000,
  
  // 文件上传配置
  upload: {
    maxSize: 1024 * 1024, // 1MB
    size: "1MB",
    allowedTypes: ['jpg', 'jpeg', 'png', 'gif']
  },
  
  // 用户角色配置
  userRoles: {
    GUEST: 'GUEST',
    USER: 'USER',
    MANAGER: 'MANAGER'
  },
  
  // 存储键名
  storageKeys: {
    token: 'token',
    userInfo: 'userInfo',
    userRole: 'userRole',
    settings: 'settings'
  }
}
