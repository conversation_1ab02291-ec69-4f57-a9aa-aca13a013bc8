// loading.js - 统一的加载状态管理
class LoadingManager {
  constructor() {
    this.loadingStack = []
    this.isShowing = false
  }

  /**
   * 显示加载提示
   * @param {string} title 加载文本
   * @param {boolean} mask 是否显示遮罩
   * @param {string} key 唯一标识，用于管理多个加载状态
   */
  show(title = '加载中...', mask = true, key = 'default') {
    // 如果已经有相同key的加载，先移除
    this.hide(key, false)
    
    // 添加到栈中
    this.loadingStack.push({ title, mask, key, timestamp: Date.now() })
    
    // 如果当前没有显示loading，则显示
    if (!this.isShowing) {
      this._showLoading(title, mask)
    }
  }

  /**
   * 隐藏加载提示
   * @param {string} key 要隐藏的加载标识
   * @param {boolean} updateUI 是否更新UI
   */
  hide(key = 'default', updateUI = true) {
    // 从栈中移除指定key的加载
    this.loadingStack = this.loadingStack.filter(item => item.key !== key)
    
    if (!updateUI) return
    
    // 如果栈为空，隐藏loading
    if (this.loadingStack.length === 0) {
      this._hideLoading()
    } else {
      // 显示栈顶的loading
      const topLoading = this.loadingStack[this.loadingStack.length - 1]
      this._showLoading(topLoading.title, topLoading.mask)
    }
  }

  /**
   * 清除所有加载状态
   */
  clear() {
    this.loadingStack = []
    this._hideLoading()
  }

  /**
   * 自动管理的加载包装器
   * @param {Function} asyncFn 异步函数
   * @param {string} title 加载文本
   * @param {string} key 唯一标识
   */
  async wrap(asyncFn, title = '加载中...', key = 'default') {
    try {
      this.show(title, true, key)
      const result = await asyncFn()
      return result
    } finally {
      this.hide(key)
    }
  }

  /**
   * 显示loading的内部方法
   */
  _showLoading(title, mask) {
    try {
      wx.showLoading({
        title,
        mask
      })
      this.isShowing = true
    } catch (error) {
      console.warn('显示loading失败:', error)
    }
  }

  /**
   * 隐藏loading的内部方法
   */
  _hideLoading() {
    try {
      wx.hideLoading()
      this.isShowing = false
    } catch (error) {
      console.warn('隐藏loading失败:', error)
    }
  }

  /**
   * 检查是否有超时的loading（防止loading卡住）
   */
  checkTimeout(timeout = 30000) {
    const now = Date.now()
    const timeoutItems = this.loadingStack.filter(item => now - item.timestamp > timeout)
    
    if (timeoutItems.length > 0) {
      console.warn('发现超时的loading:', timeoutItems)
      // 清除超时的loading
      timeoutItems.forEach(item => this.hide(item.key))
    }
  }
}

// 创建全局实例
const loadingManager = new LoadingManager()

// 定期检查超时的loading
setInterval(() => {
  loadingManager.checkTimeout()
}, 5000)

module.exports = {
  loadingManager
}
