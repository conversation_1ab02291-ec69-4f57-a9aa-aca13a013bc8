// api.js - API请求封装
const config = require('./config.js')
const BASE_URL = config.baseUrl

// 延迟加载authManager以避免循环依赖
let authManager = null
function getAuthManager() {
  if (!authManager) {
    authManager = require('./auth.js').authManager
  }
  return authManager
}

// 正在重新登录的Promise，避免并发重复登录
let reLoginPromise = null

/**
 * 封装的请求方法
 * @param {string} url 请求路径
 * @param {string} method 请求方法
 * @param {object} data 请求数据
 * @param {boolean} needAuth 是否需要认证
 * @param {boolean} isRetry 是否为重试请求
 */
function request(url, method = 'GET', data = {}, needAuth = true, isRetry = false, contentType = 'application/json') {
  return new Promise((resolve, reject) => {
    const token = wx.getStorageSync(config.storageKeys.token)
 
    // 构建请求头
    const header = {
      'Content-Type': contentType
    }

    // 如果需要认证且有token，添加Authorization头
    if (needAuth && token) {
      header['Authorization'] = `Bearer ${token}`
    }

    wx.request({
      url: `${BASE_URL}${url}`,
      method,
      data,
      header,
      success: (res) => {
        if (config.debug) {
          console.log(`API请求: ${method} ${url}`, res.data)
        }

        if (res.data.code === 'CLUB0000' || res.data.code === 'CLUB0001') {
          if (res.data.code === 'CLUB0001' && !!res.data.data) {
            // 刷新token
            const token = res.data.data;
              try {
                  const authMgr = getAuthManager();
                  if (authMgr && typeof authMgr.refreshUser === 'function') {
                      authMgr.refreshUser(token);
                  } else {
                      console.warn('authManager.refreshUser 方法不可用');
                  }
              } catch (error) {
                  console.error('调用 authManager.refreshUser 失败:', error);
              }
          }
          // 根据接口文档，小组列表接口返回的数据在page字段中
          resolve(res.data.page || res.data.data || res.data)
        } else if (res.data.error) {
          // 处理错误响应格式
          const error = res.data.error
          if (error.code === 'CLUB1001' && needAuth && !isRetry) {
            // token过期或无效，尝试自动重新登录
            handleTokenExpired(url, method, data, needAuth)
              .then(resolve)
              .catch(reject)
          } else {
            reject(error)
          }
        } else {
          reject(res.data)
        }
      },
      fail: (err) => {
        console.error(`API请求失败: ${method} ${url}`, err)
        if (!isRetry) {
          wx.showToast({
            title: '网络请求失败',
            icon: 'none'
          })
        }
        reject(err)
      }
    })
  })
}

/**
 * 处理token过期，自动重新登录并重试请求
 */
async function handleTokenExpired(url, method, data, needAuth) {
  try {
    if (config.debug) {
      console.log('Token过期，开始自动重新登录...')
    }

    // 如果已经在重新登录中，等待完成
    if (reLoginPromise) {
      await reLoginPromise
    } else {
      // 开始重新登录
      reLoginPromise = performReLogin()
      await reLoginPromise
      reLoginPromise = null
    }

    // 重新登录成功后，重试原请求
    if (config.debug) {
      console.log('重新登录成功，重试原请求:', url)
    }

    return await request(url, method, data, needAuth, true)

  } catch (error) {
    console.error('自动重新登录失败:', error)

    // 清除本地存储
    wx.removeStorageSync(config.storageKeys.token)
    wx.removeStorageSync(config.storageKeys.userInfo)
    wx.removeStorageSync(config.storageKeys.userRole)

    // 显示登录失败提示
    wx.showToast({
      title: '登录已过期，请重新操作',
      icon: 'none',
      duration: 2000
    })

    throw error
  }
}

/**
 * 执行重新登录
 */
async function performReLogin() {
  // 获取微信登录code
  const loginRes = await getWxLoginCode()

  // 直接调用登录接口，避免循环依赖
  const token = await directLogin(loginRes.code)

  // 保存新token
  wx.setStorageSync(config.storageKeys.token, token)

  if (config.debug) {
    console.log('自动重新登录成功，新token已保存')
  }

  return token
}

/**
 * 直接调用登录接口，不经过request方法包装
 */
function directLogin(code) {
  return new Promise((resolve, reject) => {
    wx.request({
      url: `${BASE_URL}/v1/auth/login`,
      method: 'POST',
      data: { code },
      header: {
        'Content-Type': 'application/json'
      },
      success: (res) => {
        if (config.debug) {
          console.log('直接登录接口响应:', res.data)
        }

        if (res.data.code === 'CLUB0000') {
          resolve(res.data.data)
        } else {
          reject(res.data.error || res.data)
        }
      },
      fail: reject
    })
  })
}

/**
 * 获取微信登录code
 */
function getWxLoginCode() {
  return new Promise((resolve, reject) => {
    wx.login({
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 用户相关API
 */
const userAPI = {
  // 登录
  login(code) {
    return request('/v1/auth/login', 'POST', { code }, false)
  },

  // 获取用户角色
  getUserRole() {
    return request('/v1/user/role', 'GET')
  },

  // 获取用户信息
  getUserInfo() {
    return request('/v1/user', 'GET')
  },

  // 更新用户信息
  updateUserProfile(data) {
    return request('/v1/user/profile', 'POST', data)
  },

  // 获取打卡记录
  getAttendanceRecords(year=0, month = 0) {
      return request(`/v1/user/attendanceRecords?year=${year}&month=${month}`, 'GET')
  },

  // 获取“我的”打卡信息
  getProfileAttendInfo() {
    return request(`/v1/user/profileAttendanceInfo`, 'GET')
  },

  // 获取用户列表（分页）
  getUserList(page = 1, size = 10, searchName = '') {
    return request(`/v1/user/list?page=${page}&size=${size}`, 'POST', { searchName })
  },

  // 分配用户角色
  assignUserRole(userId, role) {
    return request(`/v1/user/assign/${userId}`, 'POST', `role=${role}`, true, false, 'application/x-www-form-urlencoded')
  }
}

/**
 * 小组相关API
 */
const groupAPI = {
  // 获取用户的小组列表（支持分页）
  getUserGroups(page = 1, size = 3) {
    return request(`/v1/user/me/groups?page=${page}&size=${size}`, 'GET')
  },

  // 获取小组详情
  getGroupDetail(groupId) {
    return request(`/v1/group/${groupId}`, 'GET')
  },

  // 获取小组详情
  getGroupBriefDetail(groupId) {
      return request(`/v1/group/brief/${groupId}`, 'GET')
  },

  // 创建小组
  createGroup(data) {
    return request('/v1/group', 'POST', data)
  },

  // 更新小组
  updateGroup(groupId, data) {
    return request(`/v1/group/${groupId}`, 'PUT', data)
  },

  // 加入小组
  joinGroup(groupId, inviteCode) {
    return request(`/v1/group/${groupId}/join?inviteCode=${inviteCode}`, 'POST')
  },

  // 搜索小组
  searchGroup(inviteCode) {
    return request(`/v1/group/search/${inviteCode}`, 'GET')
  },

  // 推荐小组
  getRecommendedGroups() {
      return request('/v1/group/recommend', 'GET')
  },

  // 解散小组
  disbandGroup(groupId) {
    return request(`/v1/group/${groupId}/disbanded`, 'DELETE')
  },

  // 退出小组
  leaveGroup(groupId) {
    return request(`/v1/group/${groupId}/leave`, 'POST')
  },

  // 小组成员状态变更
  changeMemberStatus(groupId, userId, status) {
    return request(`/v1/group/${groupId}/change/${userId}?status=${status}`, 'POST')
  },

  // 小组成员角色变更
  changeMemberRole(groupId, userId, role) {
    return request(`/v1/group/${groupId}/assign/${userId}?role=${role}`, 'POST')
  },

  // 获取小组打卡记录
  getGroupAttendRecords(groupId, page = 0, size = 10) {
    return request(`/v1/group/${groupId}/attendRecords?page=${page}&size=${size}`, 'GET')
  },
}

/**
 * 打卡相关API
 */
const checkinAPI = {
  // 提交打卡
  submitCheckin(groupId, comment, assetUrl) {
    return request(`/v1/group/${groupId}/attend`, 'POST', {
      groupId,
      comment,
      assetUrl
    })
  },

  // 获取打卡记录
  getCheckins(params) {
    return request('/api/checkins', 'GET', params)
  },

  // 检查今日打卡状态
  getTodayStatus() {
    return request('/api/checkins/today-status', 'GET')
  }
}

/**
 * 文件上传API - 使用预上传方式
 */
const uploadAPI = {
  // 获取上传预签名URL
  getPreSignedUrl(fileName, fileType) {
    return request('/v1/upload/init', 'POST', {
      name: fileName,
      fileType: fileType
    })
  },

  // 检查上传完成状态
  checkUploadComplete(fileKey) {
    return request('/v1/upload/complete', 'POST', {
      fileKey: fileKey
    })
  },

  // 上传头像 - 使用预上传方式
  uploadAvatar(filePath) {
    return this.uploadFile(filePath)
  },

  // 上传打卡图片 - 使用预上传方式
  uploadCheckinImage(filePath) {
    return this.uploadFile(filePath)
  },

  // 通用文件上传方法
  async uploadFile(filePath) {
    try {
      // 1. 获取文件信息
      const fileInfo = await this.getFileInfo(filePath)
      const fileName = this.generateFileName(fileInfo.type)

      // 2. 获取预签名URL
      const preUploadResult = await this.getPreSignedUrl(fileName, fileInfo.type)
      const { preSignedUrl, fileKey, urlPrefix } = preUploadResult

      // 3. 使用预签名URL上传文件
      await this.uploadToPreSignedUrl(filePath, preSignedUrl, fileInfo.type)

      // 4. 检查上传完成状态
      await this.checkUploadComplete(fileKey)
      // 5. 返回文件的完整URL
      const url = `${urlPrefix}/${fileKey}`
      return {
        url: url,
        fileKey: fileKey
      }

    } catch (error) {
      console.error('文件上传失败:', error)
      throw error
    }
  },

    // 获取文件信息
    getFileInfo(filePath) {
        return new Promise((resolve, reject) => {
            wx.getFileSystemManager().getFileInfo({
                filePath,
                success: (res) => {
                    // 从文件路径推断文件类型
                    const extension = filePath.split('.').pop().toLowerCase()
                    let type = 'image/jpeg' // 默认类型

                    if (extension === 'png') {
                        type = 'image/png'
                    } else if (extension === 'gif') {
                        type = 'image/gif'
                    } else if (extension === 'webp') {
                        type = 'image/webp'
                    }

                    resolve({
                        size: res.size,
                        type: type,
                        extension: extension
                    })
                },
                fail: reject
            })
        })
    },

    // 生成文件名
    generateFileName(fileType) {
        const timestamp = Date.now()
        const random = Math.random().toString(36).substring(2, 8)
        const extension = fileType.split('/')[1] || 'jpg'
        return `${timestamp}-${random}.${extension}`
    },

    // 使用预签名URL上传文件
    uploadToPreSignedUrl(filePath, preSignedUrl, contentType) {
        return new Promise((resolve, reject) => {
            // 使用PUT方法上传到预签名URL
            this.uploadWithPutMethod(filePath, preSignedUrl, contentType)
                .then(resolve)
                .catch((putError) => {
                    // 如果PUT方法失败，尝试uploadFile方法
                    wx.uploadFile({
                        url: preSignedUrl,
                        filePath: filePath,
                        name: 'file',
                        timeout: 60000,
                        success: (res) => {
                            if (res.statusCode === 200 || res.statusCode === 204) {
                                resolve(res)
                            } else {
                                reject(new Error(`上传失败，状态码: ${res.statusCode}`))
                            }
                        },
                        fail: (uploadError) => {
                            reject(new Error(`上传失败: ${putError.message}, ${uploadError.errMsg}`))
                        }
                    })
                })
        })
    },

    // 使用PUT方法上传文件
    uploadWithPutMethod(filePath, preSignedUrl, contentType) {
        return new Promise((resolve, reject) => {
            // 读取文件内容
            wx.getFileSystemManager().readFile({
                filePath: filePath,
                success: (fileRes) => {
                    // 使用PUT方法上传
                    wx.request({
                        url: preSignedUrl,
                        method: 'PUT',
                        data: fileRes.data,
                        header: {
                            'Content-Type': contentType
                        },
                        dataType: 'other',
                        responseType: 'text',
                        timeout: 60000,
                        success: (res) => {
                            if (res.statusCode === 200 || res.statusCode === 204) {
                                resolve(res)
                            } else {
                                reject(new Error(`PUT上传失败，状态码: ${res.statusCode}`))
                            }
                        },
                        fail: (error) => {
                            reject(new Error(`PUT请求失败: ${error.errMsg || '网络错误'}`))
                        }
                    })
                },
                fail: (error) => {
                    reject(new Error(`文件读取失败: ${error.errMsg || '未知错误'}`))
                }
            })
        })
    }
}

module.exports = {
    request,
    userAPI,
    groupAPI,
    checkinAPI,
    uploadAPI
}
