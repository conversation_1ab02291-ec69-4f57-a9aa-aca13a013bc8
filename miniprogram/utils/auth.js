// auth.js - 用户认证和权限管理
const { userAPI } = require('./api.js')
const config = require('./config.js')

/**
 * 用户角色枚举
 */
const USER_ROLES = {
  GUEST: 'GUEST',
  USER: 'USER',
  MANAGER: 'MANAGER',
  CREATOR: 'CREATOR',
  ADMIN: 'ADMIN'
}

const USER_ROLES_CN = new Map([
  ['GUEST', '访客'],
  ['USER', '会员'],
  ['MANAGER', '管理员'],
  ['CREATOR', '创建者'],
  ['ADMIN', '超级管理员']
]);

/**
 * 权限配置
 */
const PERMISSIONS = {
  [USER_ROLES.GUEST]: {
    canLogin: true,
    canViewPages: true,
    canEditProfile: true,
    canCheckin: false,
    canJoinGroup: false,
    canCreateGroup: false
  },
  [USER_ROLES.USER]: {
    canLogin: true,
    canViewPages: true,
    canEditProfile: true,
    canCheckin: true,
    canJoinGroup: true,
    canCreateGroup: false
  },
  [USER_ROLES.MANAGER]: {
    canLogin: true,
    canViewPages: true,
    canEditProfile: true,
    canCheckin: true,
    canJoinGroup: true,
    canCreateGroup: true
  },
  [USER_ROLES.ADMIN]: {
    canLogin: true,
    canViewPages: true,
    canEditProfile: true,
    canCheckin: true,
    canJoinGroup: true,
    canCreateGroup: true
  }
}

/**
 * 认证管理类
 */
class AuthManager {
  constructor() {
    this.token = null
    this.userInfo = null
    this.userRole = null
  }

  /**
   * 初始化认证状态
   */
  init() {
    this.token = wx.getStorageSync(config.storageKeys.token)
    this.userInfo = wx.getStorageSync(config.storageKeys.userInfo)
    this.userRole = wx.getStorageSync(config.storageKeys.userRole)
  }

  /**
   * 微信登录
   */
  async wxLogin() {
    try {
      // 获取微信登录code
      const loginRes = await this.getWxLoginCode()
      console.log('微信登录code:', loginRes.code)

      // 调用后端登录接口
      const token = await userAPI.login(loginRes.code)
      console.log('登录成功，获取token:', token)

      // 保存token
      this.token = token
      wx.setStorageSync(config.storageKeys.token, token)

      // 解码JWT获取用户信息
      this.refreshUser(token);

      return {
        success: true,
        token: this.token,
        userInfo: this.userInfo,
        userRole: this.userRole
      }
    } catch (error) {
      console.error('登录失败:', error)
      return {
        success: false,
        error: error
      }
    }
  }

  /**
   * 获取微信登录code
   */
  getWxLoginCode() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: resolve,
        fail: reject
      })
    })
  }

  decodeJWT(token) {
    try {
      const parts = token.split('.');
      if (parts.length !== 3) {
        throw new Error('Invalid JWT');
      }

      // 解码函数（兼容小程序所有环境）
      const decodeBase64 = (base64Url) => {
        // 1. 转换为标准 base64
        const base64 = base64Url
            .replace(/-/g, '+')
            .replace(/_/g, '/')
            .padEnd(base64Url.length + (4 - base64Url.length % 4) % 4, '=');

        // 2. 使用小程序 API 解码为 ArrayBuffer
        const arrayBuffer = wx.base64ToArrayBuffer(base64);
        const uint8Array = new Uint8Array(arrayBuffer);

        // 3. 转换为字符串（兼容 TextDecoder 缺失）
        let result = '';
        for (let i = 0; i < uint8Array.length; i++) {
          result += String.fromCharCode(uint8Array[i]);
        }

        // 4. 处理 UTF-8 编码（兼容方案）
        try {
          return decodeURIComponent(escape(result));
        } catch (e) {
          // 如果解码失败，返回原始字符串
          return result;
        }
      };

      // 解码并解析 JSON
      return {
        header: JSON.parse(decodeBase64(parts[0])),
        payload: JSON.parse(decodeBase64(parts[1]))
      };
    } catch (e) {
      console.error('Failed to decode JWT:', e);
      return null;
    }
  }

  /**
   * 刷新用户
   */
  refreshUser(token) {
    if (!token && !this.isLoggedIn()) {
      console.log('未登录不处理')
      return;
    }
    token = !!token ? token : this.token
    // 解码JWT
    const { _, payload } = this.decodeJWT(token);
    console.info('解码JWT, payload:', payload);
    // 设置token
    wx.setStorageSync(config.storageKeys.token, token)
    // 移除无用字段
    const { jti, nbf, iat, exp, ...userInfo } = payload
    const userRole = userInfo.role

    this.userInfo = userInfo
    this.userRole = userRole

    // 保存到本地存储
    wx.setStorageSync(config.storageKeys.userInfo, userInfo)
    wx.setStorageSync(config.storageKeys.userRole, userRole)

    return { userInfo, userRole };
  }

  reloadAndRefreshUserInfo() {
    this.wxLogin();
    this.safeRefreshUserInfo();
  }

  /**
   * 刷新用户信息和角色
   */
  async refreshUserInfo() {
    try {
      const userInfo = await userAPI.getUserInfo()
      const userRole = userInfo.role

      console.log('获取用户信息:', userInfo)
      console.log('获取用户角色:', userRole)

      this.userInfo = userInfo
      this.userRole = userRole

      // 保存到本地存储
      wx.setStorageSync(config.storageKeys.userInfo, userInfo)
      wx.setStorageSync(config.storageKeys.userRole, userRole)

      return { userInfo, userRole }
    } catch (error) {
      console.error('获取用户信息失败:', error)

      // 如果是token过期错误，API层会自动处理重新登录
      // 这里只需要重新抛出错误，让调用方知道失败了
      throw error
    }
  }

  /**
   * 安全的刷新用户信息（带重试机制）
   */
  async safeRefreshUserInfo(showLoading = false) {
    try {
      if (showLoading) {
        wx.showLoading({
          title: '获取用户信息...',
          mask: true
        })
      }

      await this.refreshUserInfo()

      if (showLoading) {
        wx.hideLoading()
      }

      return true
    } catch (error) {
      if (showLoading) {
        wx.hideLoading()
      }

      console.error('安全刷新用户信息失败:', error)

      // 如果是网络错误或其他非认证错误，显示提示
      if (error.code !== 'CLUB1001') {
        wx.showToast({
          title: '获取用户信息失败',
          icon: 'none',
          duration: 1500
        })
      }

      return false
    }
  }

  /**
   * 检查是否已登录
   */
  isLoggedIn() {
    return !!this.token
  }

  /**
   * 获取当前用户角色
   */
  getCurrentRole() {
    return this.userRole || USER_ROLES.GUEST
  }

  /**
   * 检查用户权限
   */
  hasPermission(permission) {
    const role = this.getCurrentRole()
    return PERMISSIONS[role] && PERMISSIONS[role][permission]
  }

  /**
   * 检查是否可以打卡
   */
  canCheckin() {
    return this.hasPermission('canCheckin')
  }

  /**
   * 检查是否可以加入小组
   */
  canJoinGroup() {
    return this.hasPermission('canJoinGroup')
  }

  /**
   * 检查是否可以创建小组
   */
  canCreateGroup() {
    return this.hasPermission('canCreateGroup')
  }

  /**
   * 退出登录
   */
  logout() {
    this.token = null
    this.userInfo = null
    this.userRole = null

    // 清除本地存储
    wx.removeStorageSync(config.storageKeys.token)
    wx.removeStorageSync(config.storageKeys.userInfo)
    wx.removeStorageSync(config.storageKeys.userRole)

    console.log('用户已退出登录')
  }

  /**
   * 显示权限不足提示
   */
  showPermissionDenied(action = '此操作') {
    const role = this.getCurrentRole()
    let message = ''

    switch (role) {
      case USER_ROLES.GUEST:
        message = '游客用户无法执行此操作，请先完善个人信息'
        break
      case USER_ROLES.USER:
        message = '普通用户无法执行此操作，需要管理员权限'
        break
      default:
        message = `${action}权限不足`
    }

    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    })
  }
}

// 创建全局认证管理实例
const authManager = new AuthManager()

module.exports = {
  authManager,
  USER_ROLES,
  USER_ROLES_CN,
  PERMISSIONS,
}
