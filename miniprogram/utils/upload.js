// upload.js - 统一的文件上传工具
const { uploadAPI } = require('./api.js')
const config = require('./config.js')

/**
 * 统一的文件上传工具类
 */
class UploadManager {
  
  /**
   * 上传图片文件
   * @param {string} filePath 本地文件路径
   * @param {object} options 上传选项
   * @returns {Promise<object>} 上传结果
   */
  async uploadImage(filePath, options = {}) {
    try {
      // 显示上传进度
      if (options.showLoading !== false) {
        wx.showLoading({
          title: options.loadingText || '上传中...',
          mask: true
        })
      }

      // 验证文件
      await this.validateImageFile(filePath)

      // 执行上传
      const result = await uploadAPI.uploadFile(filePath, 'image')

      if (options.showLoading !== false) {
        wx.hideLoading()
      }
      return result

    } catch (error) {
      if (options.showLoading !== false) {
        wx.hideLoading()
      }

      console.error('图片上传失败:', error)

      // 显示错误提示
      if (options.showError !== false) {
        setTimeout(() => {
          wx.showToast({
            title: this.getErrorMessage(error),
            icon: 'none',
            duration: 2000
          })
        }, 100)
      }

      throw error
    }
  }

  /**
   * 上传头像
   * @param {string} filePath 本地文件路径
   * @returns {Promise<object>} 上传结果
   */
  async uploadAvatar(filePath) {
    return this.uploadImage(filePath, {
      loadingText: '上传头像中...',
      showError: true,
    })
  }

  /**
   * 上传打卡图片
   * @param {string} filePath 本地文件路径
   * @returns {Promise<object>} 上传结果
   */
  async uploadCheckinImage(filePath) {
    return this.uploadImage(filePath, {
      loadingText: '上传图片中...',
      showError: true,
    })
  }

  /**
   * 验证图片文件
   * @param {string} filePath 文件路径
   */
  async validateImageFile(filePath) {
    return new Promise((resolve, reject) => {
      wx.getFileSystemManager().getFileInfo({
        filePath,
        success: (res) => {
          // 检查文件大小
          if (res.size > config.upload.maxSize) {
            reject(new Error('图片大小不能超过' + config.upload.size))
            return
          }
          
          // 检查文件类型
          const extension = filePath.split('.').pop().toLowerCase()
          if (!config.upload.allowedTypes.includes(extension)) {
            reject(new Error('不支持的图片格式，请选择JPG、PNG、GIF格式的图片'))
            return
          }
          
          resolve(res)
        },
        fail: (error) => {
          reject(new Error('图片信息获取失败'))
        }
      })
    })
  }

  /**
   * 获取错误信息
   * @param {Error|object} error 错误对象
   * @returns {string} 错误信息
   */
  getErrorMessage(error) {
    if (error.message) {
      return error.message
    }
    
    if (error.description) {
      return error.description
    }
    
    if (error.code) {
      switch (error.code) {
        case 'CLUB1301':
          return '文件上传失败，请重试'
        default:
          return '上传失败，请重试'
      }
    }
    
    return '上传失败，请重试'
  }


}

// 创建单例实例
const uploadManager = new UploadManager()

module.exports = {
  UploadManager,
  uploadManager
}
