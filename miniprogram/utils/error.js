// error.js - 统一的错误处理管理
const { loadingManager } = require('./loading.js')

/**
 * 错误码映射表
 */
const ERROR_MESSAGES = {
  // 通用错误
  'NETWORK_ERROR': '网络连接失败，请检查网络设置',
  'TIMEOUT_ERROR': '请求超时，请重试',
  'UNKNOWN_ERROR': '未知错误，请重试',
  
  // 认证相关错误
  'CLUB1001': '登录已过期，请重新登录',
  'CLUB1002': '权限不足',
  'CLUB1003': '用户不存在',
  
  // 小组相关错误
  'CLUB1401': '小组名称已存在',
  'CLUB1402': '小组不存在',
  'CLUB1403': '邀请码无效',
  'CLUB1404': '小组已满员',
  
  // 打卡相关错误
  'CLUB1501': '今天已经打卡过了',
  'CLUB1502': '不在打卡时间范围内',
  'CLUB1503': '打卡内容不符合要求',
  
  // 文件上传错误
  'CLUB1601': '图片内容违规！',
  'CLUB1602': '文本内容违规！',
}

/**
 * 错误类型枚举
 */
const ERROR_TYPES = {
  NETWORK: 'network',
  AUTH: 'auth',
  BUSINESS: 'business',
  VALIDATION: 'validation',
  SYSTEM: 'system'
}

/**
 * 错误处理管理器
 */
class ErrorManager {
  constructor() {
    this.errorHistory = []
    this.maxHistorySize = 50
  }

  /**
   * 处理错误
   * @param {Error|Object} error 错误对象
   * @param {Object} options 处理选项
   */
  handle(error, options = {}) {
    const {
      showToast = true,
      logError = true,
      hideLoading = true,
      context = 'unknown'
    } = options

    // 隐藏loading
    if (hideLoading) {
      loadingManager.clear()
    }

    // 标准化错误对象
    const standardError = this._standardizeError(error, context)

    // 记录错误
    if (logError) {
      this._logError(standardError)
    }

    // 显示错误提示
    if (showToast) {
      this._showErrorToast(standardError)
    }

    return standardError
  }

  /**
   * 标准化错误对象
   */
  _standardizeError(error, context) {
    let standardError = {
      code: 'UNKNOWN_ERROR',
      message: '未知错误',
      type: ERROR_TYPES.SYSTEM,
      context,
      timestamp: new Date().toISOString(),
      original: error
    }

    if (typeof error === 'string') {
      standardError.message = error
    } else if (error && typeof error === 'object') {
      // API错误
      if (error.code) {
        standardError.code = error.code
        standardError.message = ERROR_MESSAGES[error.code] || error.description || error.message || '请求失败'
        standardError.type = this._getErrorType(error.code)
      }
      // 网络错误
      else if (error.errMsg) {
        standardError.code = 'NETWORK_ERROR'
        standardError.message = ERROR_MESSAGES.NETWORK_ERROR
        standardError.type = ERROR_TYPES.NETWORK
      }
      // 其他错误
      else {
        standardError.message = error.message || error.toString()
      }
    }

    return standardError
  }

  /**
   * 根据错误码判断错误类型
   */
  _getErrorType(code) {
    if (code.startsWith('CLUB10')) return ERROR_TYPES.AUTH
    if (code.startsWith('CLUB14')) return ERROR_TYPES.BUSINESS
    if (code.startsWith('CLUB15')) return ERROR_TYPES.BUSINESS
    if (code.startsWith('CLUB16')) return ERROR_TYPES.VALIDATION
    return ERROR_TYPES.SYSTEM
  }

  /**
   * 记录错误
   */
  _logError(error) {
    console.error(`[${error.context}] ${error.code}: ${error.message}`, error.original)
    
    // 添加到错误历史
    this.errorHistory.unshift(error)
    if (this.errorHistory.length > this.maxHistorySize) {
      this.errorHistory.pop()
    }
  }

  /**
   * 显示错误提示
   */
  _showErrorToast(error) {
    // 认证错误不显示toast，由认证管理器处理
    if (error.type === ERROR_TYPES.AUTH && error.code === 'CLUB1001') {
      return
    }

    wx.showToast({
      title: error.message,
      icon: 'none',
      duration: 3000
    })
  }

  /**
   * 获取错误历史
   */
  getErrorHistory() {
    return [...this.errorHistory]
  }

  /**
   * 清除错误历史
   */
  clearHistory() {
    this.errorHistory = []
  }

  /**
   * 检查是否为特定类型的错误
   */
  isErrorType(error, type) {
    return error && error.type === type
  }

  /**
   * 检查是否为特定错误码
   */
  isErrorCode(error, code) {
    return error && error.code === code
  }
}

// 创建全局实例
const errorManager = new ErrorManager()

/**
 * 便捷的错误处理函数
 */
function handleError(error, context = 'unknown', options = {}) {
  return errorManager.handle(error, { ...options, context })
}

/**
 * 异步函数错误包装器
 */
async function withErrorHandling(asyncFn, context = 'unknown', options = {}) {
  try {
    return await asyncFn()
  } catch (error) {
    throw handleError(error, context, options)
  }
}

module.exports = {
  errorManager,
  handleError,
  withErrorHandling,
  ERROR_TYPES,
  ERROR_MESSAGES
}
