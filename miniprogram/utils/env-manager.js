// env-manager.js - 环境配置管理器
/**
 * 环境配置管理器
 * 负责根据编译时的环境变量加载对应的配置文件
 */

// 默认环境
const DEFAULT_ENV = 'dev'

// 支持的环境列表
const SUPPORTED_ENVS = ['dev', 'uat', 'prod']

/**
 * 获取当前环境
 * 优先级：编译时环境变量 > 默认环境
 */
function getCurrentEnv() {
  // 尝试从编译时环境变量获取
  let env = DEFAULT_ENV

  // 微信小程序编译时会将 NODE_ENV 注入到全局
  if (typeof __wxConfig !== 'undefined' && __wxConfig.envVersion) {
    // 微信开发者工具的环境版本映射
    const envMap = {
      'develop': 'dev',    // 开发版
      'trial': 'uat',      // 体验版
      'release': 'prod'    // 正式版
    }
    env = envMap[__wxConfig.envVersion] || DEFAULT_ENV
  }
  
  // 如果有自定义的 NODE_ENV（通过编译前预处理设置）
  if (typeof process !== 'undefined' && process.env && process.env.NODE_ENV) {
    env = process.env.NODE_ENV
  }
  
  // 验证环境是否支持
  if (!SUPPORTED_ENVS.includes(env)) {
    console.warn(`不支持的环境: ${env}，使用默认环境: ${DEFAULT_ENV}`)
    env = DEFAULT_ENV
  }
  console.log(`当前环境: ${env}`)
  return env
}

/**
 * 加载环境配置
 */
function loadEnvConfig() {
  const env = getCurrentEnv()
  
  try {
    // 动态加载对应环境的配置文件
    let config
    switch (env) {
      case 'dev':
        config = require('../config/dev.js')
        break
      case 'uat':
        config = require('../config/uat.js')
        break
      case 'prod':
        config = require('../config/prod.js')
        break
      default:
        config = require('../config/prod.js')
    }
    
    console.log(`已加载 ${env} 环境配置:`, config)
    return config
  } catch (error) {
    console.error(`加载环境配置失败: ${env}`, error)
    // 降级到生产环境配置
    return require('../config/prod.js')
  }
}

/**
 * 获取环境信息
 */
function getEnvInfo() {
  const env = getCurrentEnv()
  const config = loadEnvConfig()
  
  return {
    env,
    baseUrl: config.baseUrl,
    appId: config.appId,
    supportedEnvs: SUPPORTED_ENVS
  }
}

module.exports = {
  getCurrentEnv,
  loadEnvConfig,
  getEnvInfo,
  SUPPORTED_ENVS,
  DEFAULT_ENV
}
