/* checkin.wxss */
.container {
  padding: 32rpx;
  background-color: #f6f6f6;
  min-height: 100vh;
}

/* 日期信息 */
.date-info {
  text-align: center;
  margin-bottom: 48rpx;
}

.date-number {
  display: block;
  font-size: 96rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.date-text {
  display: block;
  font-size: 28rpx;
  color: #666;
}

/* 上传区域 */
.upload-section {
  margin-bottom: 48rpx;
}

.section-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 24rpx;
}

.upload-area {
  background: white;
  border: 4rpx dashed #ddd;
  border-radius: 24rpx;
  padding: 64rpx 32rpx;
  text-align: center;
}

.upload-icon {
  font-size: 96rpx;
  margin-bottom: 24rpx;
}

.upload-text {
  display: block;
  font-size: 32rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.upload-hint {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 32rpx;
}

.upload-buttons {
  display: flex;
  justify-content: center;
  gap: 32rpx;
}

.upload-btn {
  padding: 16rpx 32rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  border: none;
}

.upload-btn.camera {
  background: #667eea;
  color: white;
}

.upload-btn.album {
  background: #666;
  color: white;
}

/* 图片预览 */
.image-preview {
  background: white;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);
}

.preview-image {
  width: 100%;
  height: 400rpx;
}

.image-actions {
  display: flex;
  padding: 24rpx;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  padding: 16rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  border: none;
  background: #f5f5f5;
  color: #333;
}

.action-btn.delete {
  background: #ffebee;
  color: #f44336;
}

/* 备注区域 */
.note-section {
  margin-bottom: 48rpx;
}

.note-input {
  width: 100%;
  min-height: 192rpx;
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  border: none;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);
}

.note-count {
  display: block;
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 16rpx;
}

/* 小组选择 */
.group-section {
  margin-bottom: 64rpx;
}

.group-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.group-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: white;
  border-radius: 24rpx;
  padding: 24rpx;
  border: 4rpx solid transparent;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);
}

.group-option.selected {
  border-color: #667eea;
  background: #f8faff;
}

.group-option-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.group-option-left radio {
  margin-right: 24rpx;
}

.group-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  font-size: 28rpx;
}

.group-icon.blue {
  background: #e3f2fd;
  color: #1976d2;
}

.group-icon.purple {
  background: #f3e5f5;
  color: #7b1fa2;
}

.group-icon.green {
  background: #e8f5e8;
  color: #388e3c;
}

.group-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

/* 小组状态样式 */
.group-status {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  margin-left: 16rpx;
}

.group-status.completed {
  background: #e8f5e8;
  color: #4caf50;
}

.group-status.pending {
  background: #fff3e0;
  color: #ff9800;
}

.group-status.warning {
  background: #ffebee;
  color: #f44336;
}

/* 提交按钮 */
.submit-btn {
  width: 100%;
  padding: 32rpx;
  border-radius: 24rpx;
  font-size: 36rpx;
  font-weight: 600;
  border: none;
  box-shadow: 0 8rpx 40rpx rgba(102, 126, 234, 0.3);
}

.submit-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.submit-btn.disabled {
  background: #ddd;
  color: #999;
  box-shadow: none;
}

/* 小组加载状态样式 */
.load-more-groups {
  padding: 32rpx 0;
  text-align: center;
}

.loading-text,
.no-more-text,
.pull-up-text {
  color: #999;
  font-size: 24rpx;
}

.loading-text {
  color: #667eea;
}

.empty-groups {
  padding: 64rpx 0;
  text-align: center;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
}
