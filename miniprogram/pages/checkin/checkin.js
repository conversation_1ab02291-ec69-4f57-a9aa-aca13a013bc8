// checkin.js
const { authManager, USER_ROLES } = require('../../utils/auth.js')
const { groupAPI, checkinAPI } = require('../../utils/api.js')
const { uploadManager } = require('../../utils/upload.js')

const config = require('../../utils/config.js')



Page({
  data: {
    todayNumber: '',
    todayText: '',
    imageUrl: '',
    noteText: '',
    selectedGroupId: null,
    groupList: [],
    submitting: false,
    canSubmit: false,
    // 分页相关数据
    currentPage: 0,
    pageSize: 3,
    hasMore: true,
    loading: false,
    stopLoading: false,
    size: config.upload.size,
  },

  onLoad(options) {
    this.checkPermission()
    this.initPage()
    if (options.groupId) {
      this.loadSelectedGroup(options.groupId)
    } else {
      this.loadGroups()
    }

  },

  /**
   * 页面滚动到底部时触发
   */
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading && !this.data.stopLoading) {
      this.loadMoreGroups()
    }
  },

  /**
   * 检查打卡权限
   */
  checkPermission() {
    try {
      authManager.refreshUser()

      if (!authManager.canCheckin()) {
        wx.showModal({
          title: '权限不足',
          content: '您当前没有打卡权限，请先完善个人信息',
          showCancel: false,
          success: () => {
            wx.navigateBack()
          }
        })
        return
      }
    } catch (error) {
      console.error('权限检查失败:', error)
      wx.showToast({
        title: '权限检查失败',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  initPage() {
    this.setDateInfo()
    this.checkCanSubmit()
  },

  /**
   * 加载小组列表
   */
  async loadGroups() {
    this.setData({
      groupList: [],
      currentPage: 0,
      hasMore: true,
      loading: false
    })
    await this.loadMoreGroups()
  },

  async loadSelectedGroup(groupId) {
    try {
      const res = await groupAPI.getGroupBriefDetail(groupId)
      const transformedGroup = this.transformGroupsData([{group:res}])[0]
      this.setData({
        selectedGroupId: parseInt(groupId),
        groupList: [transformedGroup],
        stopLoading: true
      })
    } catch (error) {
      console.error('加载小组详情失败:', error)
      wx.showToast({
        title: '加载小组详情失败',
        icon: 'none'
      })
    }
  },

  /**
   * 加载更多小组数据
   */
  async loadMoreGroups() {
    if (this.data.loading || !this.data.hasMore) {
      return
    }

    this.setData({ loading: true })

    try {
      const nextPage = this.data.currentPage + 1
      console.log(`加载第${nextPage}页小组数据`)

      const response = await groupAPI.getUserGroups(nextPage, this.data.pageSize)
      const newGroups = this.transformGroupsData(response.content.filter(item => item.status !== 'PENDING') || [])

      // 合并新数据到现有列表
      const updatedGroupList = [...this.data.groupList, ...newGroups]

      // 检查是否还有更多数据
      const hasMore = response.page && (response.page.number + 1) < response.page.totalPages

      this.setData({
        groupList: updatedGroupList,
        currentPage: nextPage,
        hasMore: hasMore
      })

      // 如果是第一次加载且有数据，自动选择第一个小组
      if (nextPage === 1 && newGroups.length > 0 && !this.data.selectedGroupId) {
        this.setData({
          selectedGroupId: newGroups[0].id
        }, () => {
          this.checkCanSubmit()
        })
      }

      console.log(`第${nextPage}页加载完成，共${newGroups.length}条数据，还有更多：${hasMore}`)

    } catch (error) {
      console.error('加载小组列表失败:', error)
      wx.showToast({
        title: '加载小组失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 转换接口数据为页面所需格式
   */
  transformGroupsData(apiGroups) {
    return apiGroups.map(item => {
      const group = item.group
      const metadata = group.metadata || {}

      // 计算打卡状态
      let status = 'pending'
      let statusIcon = '⏰'

      if (item.attendedToday) {
        status = 'completed'
        statusIcon = '✓'
      } else if (metadata.targetCount && item.currentCount >= metadata.targetCount) {
        status = 'completed'
        statusIcon = '✓'
      } else if (metadata.targetCount && item.currentCount < metadata.targetCount * 0.5) {
        status = 'warning'
        statusIcon = '!'
      }

      return {
        id: group.id,
        name: group.name || '未命名小组',
        icon: metadata.icon || '📝',
        iconColor: metadata.iconColor || 'blue',
        status: status,
        statusIcon: statusIcon
      }
    })
  },

  setDateInfo() {
    const today = new Date()
    const day = today.getDate()
    const year = today.getFullYear()
    const month = today.getMonth() + 1
    const weekDay = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'][today.getDay()]

    this.setData({
      todayNumber: day.toString(),
      todayText: `${year}年${month}月 · ${weekDay}`
    })
  },

  // 选择图片（通用）
  chooseImage() {
    wx.showActionSheet({
      itemList: ['拍照', '从相册选择'],
      success: (res) => {
        let sourceType = []
        if (res.tapIndex === 0) {
          sourceType = ['camera']
        } else if (res.tapIndex === 1) {
          sourceType = ['album']
        } else {
          // 用户取消选择
          return
        }

        setTimeout(() => {
          wx.chooseMedia({
            count: 1,
            mediaType: ['image'],
            sourceType: sourceType,
            success: (mediaRes) => {
              this.handleImageSuccess(mediaRes.tempFiles[0])
            },
            fail: (err) => {
              console.error('选择媒体失败:', err)
            }
          })
        }, 300)
      },
      fail: (err) => {
        // 统一处理 actionSheet 的失败情况
        if (err.errMsg !== 'showActionSheet:fail cancel') {
          console.error('显示操作菜单失败:', err)
        }
      }
    })
  },

  // 处理图片选择成功
  handleImageSuccess(file) {
    // 检查文件大小
    if (file.size > config.upload.maxSize) {
      wx.showToast({
        title: '图片大小不能超过' + config.upload.size,
        icon: 'none'
      })
      return
    }

    this.setData({
      imageUrl: file.tempFilePath
    }, () => {
      this.checkCanSubmit()
    })
  },

  // 删除图片
  deleteImage() {
    this.setData({
      imageUrl: ''
    }, () => {
      this.checkCanSubmit()
    })
  },

  // 备注输入
  onNoteInput(e) {
    this.setData({
      noteText: e.detail.value
    })
  },

  // 选择小组
  selectGroup(e) {
    const groupId = e.currentTarget.dataset.id
    this.setData({
      selectedGroupId: groupId
    }, () => {
      this.checkCanSubmit()
    })
  },

  // 检查是否可以提交
  checkCanSubmit() {
    const { imageUrl, selectedGroupId } = this.data
    const canSubmit = imageUrl && selectedGroupId

    this.setData({
      canSubmit
    })
  },

  // 提交打卡
  async submitCheckin() {
    if (!this.data.canSubmit || this.data.submitting) {
      return
    }

    this.setData({
      submitting: true
    })

    try {
      let assetUrl = ''

      // 如果有图片，先上传图片
      if (this.data.imageUrl) {
        try {
          const uploadResult = await uploadManager.uploadCheckinImage(this.data.imageUrl)
          assetUrl = uploadResult.url
          console.log('打卡图片上传成功:', assetUrl)
        } catch (uploadError) {
          console.error('图片上传失败:', uploadError)
          wx.showToast({
            title: '图片上传失败，请重试',
            icon: 'none'
          })
          return
        }
      }
      wx.showLoading({
        title: '内容审核中...'
      })

      // 调用真实的打卡接口
      await checkinAPI.submitCheckin(
        this.data.selectedGroupId,
        this.data.noteText || '',
        assetUrl
      )

      // 保存打卡记录到本地（使用上传后的URL）
      this.saveCheckinRecord(assetUrl)

      wx.showToast({
        title: '打卡成功！',
        icon: 'success'
      })

      wx.hideLoading();

      // 延迟返回首页
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)

    } catch (error) {
      console.error('打卡失败:', error)
      let errorMessage = error.description || '打卡失败，请重试'
      // 处理特定的错误码
      if (error.code === 'CLUB1501') {
        errorMessage = '今天已打卡'
      } else if (error.code === 'CLUB1601') {
        errorMessage = '图片内容违规！'
      } else if (error.code === 'CLUB1602') {
        errorMessage = '文字内容违规！'
      }
      wx.hideLoading();
      wx.showToast({
        title: errorMessage,
        icon: 'none'
      })
    } finally {
      this.setData({
        submitting: false
      })
    }
  },



  // 保存打卡记录到本地存储
  saveCheckinRecord(uploadedImageUrl) {
    const today = new Date()
    const todayKey = `${today.getFullYear()}-${today.getMonth() + 1}-${today.getDate()}`

    const checkinData = {
      date: todayKey,
      imageUrl: uploadedImageUrl || this.data.imageUrl, // 优先使用上传后的URL
      note: this.data.noteText,
      groupId: this.data.selectedGroupId,
      timestamp: Date.now()
    }

    // 保存今日打卡状态
    wx.setStorageSync(`checkin_${todayKey}`, true)

    // 保存打卡详情
    wx.setStorageSync(`checkin_detail_${todayKey}`, checkinData)
  }
})
