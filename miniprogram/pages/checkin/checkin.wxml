<!--checkin.wxml-->
<view class="container">
  <!-- 日期信息 -->
  <view class="date-info">
    <text class="date-number">{{todayNumber}}</text>
    <text class="date-text">{{todayText}}</text>
  </view>

  <!-- 上传图片区域 -->
  <view class="upload-section">
    <text class="section-label">上传打卡图片 *</text>
    <view class="upload-area" bindtap="chooseImage" wx:if="{{!imageUrl}}">
      <view class="upload-icon">📷</view>
      <text class="upload-text">点击上传图片</text>
      <text class="upload-hint">支持 JPG、PNG 格式，大小不超过 {{size}}</text>
      <view class="upload-buttons">
        <button class="upload-btn camera" bindtap="takePhoto">
          📷 拍照
        </button>
        <button class="upload-btn album" bindtap="chooseFromAlbum">
          🖼️ 相册
        </button>
      </view>
    </view>
    
    <!-- 已选择的图片 -->
    <view class="image-preview" wx:if="{{imageUrl}}">
      <image src="{{imageUrl}}" mode="aspectFill" class="preview-image"></image>
      <view class="image-actions">
        <button class="action-btn" bindtap="chooseImage">重新选择</button>
        <button class="action-btn delete" bindtap="deleteImage">删除</button>
      </view>
    </view>
  </view>

  <!-- 文字备注 -->
  <view class="note-section">
    <text class="section-label">添加备注</text>
    <textarea 
      placeholder="分享今天的心情或感受..."
      class="note-input"
      value="{{noteText}}"
      bindinput="onNoteInput"
      maxlength="100"
    ></textarea>
    <text class="note-count">{{noteText.length}}/100</text>
  </view>

  <!-- 选择小组 -->
  <view class="group-section">
    <text class="section-label">选择小组 *</text>
    <view class="group-list">
      <view
        class="group-option {{selectedGroupId === item.id ? 'selected' : ''}}"
        wx:for="{{groupList}}"
        wx:key="id"
        bindtap="selectGroup"
        data-id="{{item.id}}"
      >
        <view class="group-option-left">
          <radio checked="{{selectedGroupId === item.id}}" color="#667eea"/>
          <view class="group-icon {{item.iconColor}}">
            <text>{{item.icon}}</text>
          </view>
          <text class="group-name">{{item.name}}</text>
        </view>
        <view class="group-status {{item.status}}">
          <text class="iconfont">{{item.statusIcon}}</text>
        </view>
      </view>

      <!-- 加载更多提示 -->
      <view class="load-more-groups" wx:if="{{groupList.length > 0 && !stopLoading}}">
        <view class="loading-groups" wx:if="{{loading}}">
          <text class="loading-text">加载中...</text>
        </view>
        <view class="no-more-groups" wx:elif="{{!hasMore}}">
          <text class="no-more-text">没有更多小组了</text>
        </view>
        <view class="pull-up-groups" wx:else>
          <text class="pull-up-text">上拉加载更多小组</text>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-groups" wx:if="{{groupList.length === 0 && !loading}}">
        <text class="empty-text">暂无可选择的小组</text>
      </view>
    </view>
  </view>

  <!-- 提交按钮 -->
  <button 
    class="submit-btn {{canSubmit ? 'active' : 'disabled'}}" 
    bindtap="submitCheckin"
    disabled="{{!canSubmit || submitting}}"
  >
    {{submitting ? '提交中...' : '完成打卡'}}
  </button>
</view>
