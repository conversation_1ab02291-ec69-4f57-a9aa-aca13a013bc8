// statistics.js
const { authManager, USER_ROLES } = require('../../utils/auth.js')

// Mock数据
const mockData = {
  monthOverview: {
    checkedDays: 18,
    consecutiveDays: 7,
    completionRate: 82,
    joinedGroups: 3
  },
  weeklyTrend: [
    { day: '周一', percentage: 60 },
    { day: '周二', percentage: 80 },
    { day: '周三', percentage: 90 },
    { day: '周四', percentage: 70 },
    { day: '周五', percentage: 85 },
    { day: '周六', percentage: 40 },
    { day: '周日', percentage: 30 }
  ],
  groupRanking: [
    {
      id: 1,
      rank: 1,
      rankClass: 'gold',
      name: '晨跑俱乐部',
      icon: '🏃',
      iconColor: 'blue',
      currentCount: 18,
      targetCount: 22,
      completionRate: 82,
      rateClass: 'high'
    },
    {
      id: 2,
      rank: 2,
      rankClass: 'silver',
      name: '读书分享会',
      icon: '📚',
      iconColor: 'purple',
      currentCount: 15,
      targetCount: 20,
      completionRate: 75,
      rateClass: 'medium'
    },
    {
      id: 3,
      rank: 3,
      rankClass: 'bronze',
      name: '健身打卡',
      icon: '💪',
      iconColor: 'green',
      currentCount: 12,
      targetCount: 25,
      completionRate: 48,
      rateClass: 'low'
    }
  ],
  achievements: [
    {
      id: 1,
      name: '连续7天',
      icon: '🏆',
      color: 'yellow',
      unlocked: true
    },
    {
      id: 2,
      name: '月度达人',
      icon: '🥇',
      color: 'blue',
      unlocked: true
    },
    {
      id: 3,
      name: '坚持之星',
      icon: '⭐',
      color: 'green',
      unlocked: true
    },
    {
      id: 4,
      name: '小组之王',
      icon: '👑',
      color: 'purple',
      unlocked: true
    },
    {
      id: 5,
      name: '待解锁',
      icon: '🔒',
      color: 'gray',
      unlocked: false
    },
    {
      id: 6,
      name: '待解锁',
      icon: '🔒',
      color: 'gray',
      unlocked: false
    }
  ]
}

Page({
  data: {
    monthOverview: mockData.monthOverview,
    weeklyTrend: mockData.weeklyTrend,
    groupRanking: mockData.groupRanking,
    achievements: mockData.achievements,
    userRole: USER_ROLES.GUEST
  },

  onLoad() {
    this.checkUserRole()
    this.loadStatistics()
  },

  onShow() {
    this.checkUserRole()
    this.loadStatistics()
  },

  /**
   * 检查用户角色
   */
  checkUserRole() {
    return authManager.getCurrentRole()
  },

  // 加载统计数据
  loadStatistics() {
    // 这里可以从本地存储或服务器加载真实数据
    // 目前使用mock数据
    this.setData({
      monthOverview: mockData.monthOverview,
      weeklyTrend: mockData.weeklyTrend,
      groupRanking: mockData.groupRanking,
      achievements: mockData.achievements
    })
  },

  // 计算本月统计数据
  calculateMonthStats() {
    // 从本地存储获取打卡记录
    // const history = wx.getStorageSync('checkin_history') || []
    const currentMonth = new Date().getMonth()
    const currentYear = new Date().getFullYear()
    
    // 筛选本月数据
    const monthData = history.filter((record) => {
      const recordDate = new Date(record.timestamp)
      return recordDate.getMonth() === currentMonth && 
             recordDate.getFullYear() === currentYear
    })

    // 计算统计数据
    const checkedDays = monthData.length
    const consecutiveDays = this.calculateConsecutiveDays(history)
    const completionRate = Math.round((checkedDays / 30) * 100)
    
    return {
      checkedDays,
      consecutiveDays,
      completionRate,
      joinedGroups: 3 // 从小组数据获取
    }
  },

  // 计算连续打卡天数
  calculateConsecutiveDays(history) {
    if (history.length === 0) return 0
    
    let consecutive = 0
    const today = new Date()
    
    // 从今天开始往前计算连续天数
    for (let i = 0; i < 30; i++) {
      const checkDate = new Date(today)
      checkDate.setDate(today.getDate() - i)
      
      const dateKey = `${checkDate.getFullYear()}-${checkDate.getMonth() + 1}-${checkDate.getDate()}`
      const hasCheckin = history.some((record) => record.date === dateKey)
      
      if (hasCheckin) {
        consecutive++
      } else {
        break
      }
    }
    
    return consecutive
  },


})
