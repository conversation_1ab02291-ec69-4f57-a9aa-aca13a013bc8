/* statistics.wxss */
.page-container {
  padding: 32rpx;
  background-color: #f6f6f6;
  min-height: 100vh;
}

/* 测试样式 */
.test-content {
  background: #9c27b0;
  color: white;
  padding: 40rpx;
  border-radius: 20rpx;
  text-align: center;
  margin-bottom: 40rpx;
}

.test-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.test-subtitle {
  display: block;
  font-size: 28rpx;
}

/* 卡片通用样式 */
.overview-card,
.trend-card,
.ranking-card,
.achievements-card {
  background: white;
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);
}

.card-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 32rpx;
}

/* 总览卡片 */
.overview-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.overview-card .card-title {
  color: white;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 32rpx;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  opacity: 0.9;
}

/* 趋势图 */
.chart-container {
  margin-bottom: 24rpx;
}

.chart-bars {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  height: 240rpx;
  margin-bottom: 16rpx;
  padding: 0 16rpx;
}

.chart-bar {
  flex: 1;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  margin: 0 8rpx;
}

.bar-fill {
  width: 48rpx;
  background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
  border-radius: 24rpx 24rpx 0 0;
  min-height: 16rpx;
}

.chart-labels {
  display: flex;
  justify-content: space-between;
  padding: 0 24rpx;
}

.chart-label {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}

.chart-note {
  display: block;
  text-align: center;
  font-size: 24rpx;
  color: #666;
}

/* 排行榜 */
.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.ranking-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ranking-info {
  display: flex;
  align-items: center;
}

.ranking-badge {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  font-weight: bold;
  font-size: 24rpx;
}

.ranking-badge.gold {
  background: #ffd700;
  color: #b8860b;
}

.ranking-badge.silver {
  background: #c0c0c0;
  color: #696969;
}

.ranking-badge.bronze {
  background: #cd7f32;
  color: #8b4513;
}

.ranking-badge.normal {
  background: #f0f0f0;
  color: #666;
}

.group-info {
  display: flex;
  align-items: center;
}

.group-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  font-size: 28rpx;
}

.group-icon.blue {
  background: #e3f2fd;
  color: #1976d2;
}

.group-icon.purple {
  background: #f3e5f5;
  color: #7b1fa2;
}

.group-icon.green {
  background: #e8f5e8;
  color: #388e3c;
}

.group-name {
  display: block;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}

.group-progress {
  display: block;
  font-size: 24rpx;
  color: #666;
}

.completion-rate {
  text-align: right;
}

.rate-number {
  font-size: 36rpx;
  font-weight: bold;
}

.completion-rate.high .rate-number {
  color: #4caf50;
}

.completion-rate.medium .rate-number {
  color: #ff9800;
}

.completion-rate.low .rate-number {
  color: #f44336;
}

/* 成就徽章 */
.achievements-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32rpx;
}

.achievement-item {
  text-align: center;
}

.achievement-icon {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16rpx;
  font-size: 40rpx;
}

.achievement-icon.yellow {
  background: #fff3e0;
  color: #f57c00;
}

.achievement-icon.blue {
  background: #e3f2fd;
  color: #1976d2;
}

.achievement-icon.green {
  background: #e8f5e8;
  color: #388e3c;
}

.achievement-icon.purple {
  background: #f3e5f5;
  color: #7b1fa2;
}

.achievement-icon.gray {
  background: #f5f5f5;
  color: #999;
}

.achievement-name {
  display: block;
  font-size: 24rpx;
  color: #666;
}

.achievement-item.unlocked .achievement-name {
  color: #333;
  font-weight: 500;
}

.achievement-item.locked {
  opacity: 0.5;
}
