// groups.js
const { authManager, USER_ROLES, USER_ROLES_CN } = require('../../utils/auth.js')
const { groupAPI } = require('../../utils/api.js')
const config = require("../../utils/config");

Page({
  data: {
    groupList: [],
    // 分页相关数据
    currentPage: 0,
    pageSize: 3,
    hasMore: true,
    loading: false,
    // 标记是否是首次加载
    isFirstLoad: true,
    pageLoading: true,
    // 权限相关数据
    userRole: USER_ROLES.GUEST,
    canJoinGroup: false,
    canCreateGroup: false,
    ossPrefixUrl: config.ossPrefixUrl,
  },

  onLoad() {
    this.checkUserRole()
    this.resetAndLoadGroups()
  },

  onShow() {
    this.checkUserRole()

    // 如果不是首次加载，重新加载数据以获取最新状态
    if (!this.data.isFirstLoad) {
      this.resetAndLoadGroups()
    } else {
      // 标记首次加载完成
      this.setData({ isFirstLoad: false })
    }
  },

  /**
   * 页面滚动到底部时触发
   */
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreGroups()
    }
  },

  /**
   * 检查用户角色和权限
   */
  checkUserRole() {
    const userRole = authManager.getCurrentRole()
    const canJoinGroup = authManager.canJoinGroup()
    const canCreateGroup = authManager.canCreateGroup()

    this.setData({
      userRole,
      canJoinGroup,
      canCreateGroup
    })
  },

  /**
   * 重置并加载小组列表
   */
  async resetAndLoadGroups() {
    // 防止重复调用
    if (this.data.loading) {
      return
    }

    this.setData({
      groupList: [],
      currentPage: 0,
      hasMore: true,
      loading: false,
      pageLoading: true
    })
    try {
      await this.loadMoreGroups()
    } finally {
      this.setData({ pageLoading: false })
    }
  },

  /**
   * 加载更多小组数据
   */
  async loadMoreGroups() {
    if (this.data.loading || !this.data.hasMore) {
      return
    }

    this.setData({ loading: true })

    try {
      const nextPage = this.data.currentPage + 1
      console.log(`加载第${nextPage}页小组数据`)

      const response = await groupAPI.getUserGroups(nextPage, this.data.pageSize)
      const newGroups = this.transformGroupsData(response.content || [])

      // 合并新数据到现有列表
      const updatedGroupList = [...this.data.groupList, ...newGroups]

      // 检查是否还有更多数据
      const hasMore = response.page && (response.page.number + 1) < response.page.totalPages

      this.setData({
        groupList: updatedGroupList,
        currentPage: nextPage,
        hasMore: hasMore
      })

      console.log(`第${nextPage}页加载完成，共${newGroups.length}条数据，还有更多：${hasMore}`)

    } catch (error) {
      console.error('加载小组列表失败:', error)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 转换接口数据为页面所需格式
   */
  transformGroupsData(apiGroups) {
    return apiGroups.map(item => {
      const group = item.group
      const metadata = group.metadata || {}
      const groupMembers = group.groupMembers || []

      // 计算进度百分比和颜色
      const targetCount = metadata.targetCount || 30
      const currentCount = item.currentCount || 0
      const progressPercent = Math.min(Math.round((currentCount / targetCount) * 100), 100)

      let progressColor = 'red'
      let status = 'warning'
      let statusIcon = '⏰'

      if (item.status === 'PENDING') {
        status = 'pending'
      } else if (item.attendedToday) {
        status = 'completed'
        statusIcon = '✔️'
        progressColor = 'green'
      } else if (progressPercent >= 80) {
        progressColor = 'green'
      } else if (progressPercent >= 50) {
        progressColor = 'yellow'
      }

      // 处理成员头像
      const recentMembers = groupMembers.slice(0, 3).map(member => ({
        id: (member.user && member.user.id) || Math.random(),
        avatar: (member.user && member.user.wxUser && member.user.wxUser.avatar) || config.defaultAvatarUrl
      }))

      return {
        id: group.id,
        name: group.name || '未命名小组',
        icon: metadata.icon || '📝',
        iconColor: metadata.iconColor || 'blue',
        memberCount: groupMembers.length,
        role: item.role || 'USER',
        roleCN: USER_ROLES_CN.get(item.role),
        currentCount: currentCount,
        targetCount: targetCount,
        status: status,
        statusIcon: statusIcon,
        progressPercent: progressPercent,
        progressColor: progressColor,
        recentMembers: recentMembers
      }
    })
  },

  // 创建小组
  createGroup() {
    if (!this.data.canCreateGroup) {
      authManager.showPermissionDenied('创建小组')
      return
    }

    wx.navigateTo({
      url: '/pages/create-group/create-group'
    })
  },

  // 加入小组
  joinGroup() {
    if (!this.data.canJoinGroup) {
      authManager.showPermissionDenied('加入小组')
      return
    }

    wx.navigateTo({
      url: '/pages/join-group/join-group'
    })
  },

  // 查看小组详情
  goToGroupDetail(e) {
    const status = e.currentTarget.dataset.status
    if (status === 'pending') {
      wx.showToast({
        title: '管理员全速审核中...',
        icon: 'none'
      })
      return;
    }

    const groupId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/group-detail/group-detail?id=${groupId}`
    })
  },


})
