/* groups.wxss */

/* 权限提示样式 */
.permission-tips {
  background: rgba(255, 193, 7, 0.1);
  border: 2rpx solid rgba(255, 193, 7, 0.3);
  border-radius: 16rpx;
  padding: 24rpx;
  text-align: center;
  margin: 16rpx 0;
}

.tip-text {
  color: #ff9800;
  font-size: 28rpx;
}

/* 加载更多样式 */
.load-more {
  padding: 40rpx 0;
  text-align: center;
}

.loading-text,
.no-more-text,
.pull-up-text {
  color: #999;
  font-size: 28rpx;
}

.loading-text {
  color: #007aff;
}
.page-container {
  padding: 32rpx;
  background-color: #f6f6f6;
  min-height: 100vh;
}

/* 测试样式 */
.test-content {
  background: #4caf50;
  color: white;
  padding: 40rpx;
  border-radius: 20rpx;
  text-align: center;
  margin-bottom: 40rpx;
}

.test-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.test-subtitle {
  display: block;
  font-size: 28rpx;
}

/* 快速操作 */
.quick-actions {
  display: flex;
  gap: 24rpx;
  margin-bottom: 48rpx;
}

.action-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 24rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn.primary {
  background: #667eea;
  color: white;
  box-shadow: 0 8rpx 40rpx rgba(102, 126, 234, 0.3);
}

.action-btn.secondary {
  background: white;
  color: #333;
  border: 2rpx solid #e0e0e0;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);
}

/* 小组列表 */
.groups-list {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.group-card {
  background: white;
  border-radius: 32rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease;
}

.group-card:active {
  transform: scale(0.98);
}

/* 小组头部 */
.group-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32rpx;
}

.group-info {
  display: flex;
  align-items: center;
}

.group-icon {
  width: 96rpx;
  height: 96rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 32rpx;
  font-size: 40rpx;
}

.group-icon.blue {
  background: #e3f2fd;
  color: #1976d2;
}

.group-icon.purple {
  background: #f3e5f5;
  color: #7b1fa2;
}

.group-icon.green {
  background: #e8f5e8;
  color: #388e3c;
}

.group-name {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.group-meta {
  display: block;
  font-size: 24rpx;
  color: #666;
}

.group-status {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.group-status.completed {
  background: #e8f5e8;
  color: #4caf50;
}

.group-status.pending {
  background: #fff3e0;
  color: #ff9800;
}

.group-status.warning {
  background: #ffebee;
  color: #f44336;
}

/* 进度条 */
.progress-section {
  margin-bottom: 32rpx;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.progress-label {
  font-size: 28rpx;
  color: #666;
}

.progress-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.progress-bar {
  width: 100%;
  height: 16rpx;
  background: #f0f0f0;
  border-radius: 8rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 8rpx;
  transition: width 0.3s ease;
}

.progress-fill.green {
  background: linear-gradient(90deg, #4caf50 0%, #81c784 100%);
}

.progress-fill.yellow {
  background: linear-gradient(90deg, #ff9800 0%, #ffb74d 100%);
}

.progress-fill.red {
  background: linear-gradient(90deg, #f44336 0%, #e57373 100%);
}

/* 成员区域 */
.members-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.member-avatars {
  display: flex;
  align-items: center;
}

.member-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  border: 4rpx solid white;
  margin-left: -16rpx;
}

.member-avatar:first-child {
  margin-left: 0;
}

.more-members {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: #f0f0f0;
  border: 4rpx solid white;
  margin-left: -16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: #666;
}

.detail-link {
  font-size: 28rpx;
  color: #667eea;
  font-weight: 500;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.empty-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.empty-subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 48rpx;
}

.empty-action {
  background: #667eea;
  color: white;
  border: none;
  border-radius: 24rpx;
  padding: 24rpx 48rpx;
  font-size: 32rpx;
  font-weight: 500;
}
