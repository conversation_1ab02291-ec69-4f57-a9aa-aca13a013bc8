<!--groups.wxml-->
<loading show="{{pageLoading}}"></loading>
<view class="page-container">
  <!-- 测试内容 -->
  <view class="test-content">
    <text class="test-title">我的小组</text>
    <text class="test-subtitle">管理你的打卡小组</text>
  </view>

  <!-- 快速操作 -->
  <view class="quick-actions">
    <!-- 根据权限显示创建小组按钮 -->
    <button
      class="action-btn primary"
      bindtap="createGroup"
      wx:if="{{canCreateGroup}}"
    >
      ➕ 创建小组
    </button>
    <!-- 根据权限显示加入小组按钮 -->
    <button
      class="action-btn secondary"
      bindtap="joinGroup"
      wx:if="{{canJoinGroup}}"
    >
      🔍 加入小组
    </button>
    <!-- 权限不足提示 -->
    <view class="permission-tips" wx:if="{{!canJoinGroup && !canCreateGroup}}">
      <text class="tip-text">{{userRole === 'GUEST' ? '完善个人信息后可加入或创建小组' : '暂无小组操作权限'}}</text>
    </view>
  </view>

  <!-- 小组列表 -->
  <view class="groups-list">
    <view 
      class="group-card" 
      wx:for="{{groupList}}" 
      wx:key="id"
      bindtap="goToGroupDetail"
      data-id="{{item.id}}"
      data-status="{{item.status}}"
    >
      <!-- 小组头部 -->
      <view class="group-header">
        <view class="group-info">
          <view class="group-icon {{item.iconColor}}">
            <text>{{item.icon}}</text>
          </view>
          <view class="group-details">
            <text class="group-name">{{item.name}}</text>
            <text class="group-meta">{{item.memberCount}}名成员 · {{item.roleCN}}</text>
          </view>
        </view>
        <view class="group-status {{item.status}}" wx:if="{{item.status !== 'pending'}}">
          <text>{{item.statusIcon}}</text>
        </view>
      </view>

      <!-- 进度条 -->
      <view class="progress-section">
        <view class="progress-info">
          <text class="progress-label">本月进度</text>
          <text class="progress-text">{{item.currentCount}}/{{item.targetCount}}</text>
        </view>
        <view class="progress-bar">
          <view 
            class="progress-fill {{item.progressColor}}" 
            style="width: {{item.progressPercent}}%"
          ></view>
        </view>
      </view>

      <!-- 成员头像 -->
      <view class="members-section">
        <view class="member-avatars">
          <image 
            class="member-avatar" 
            wx:for="{{item.recentMembers}}" 
            wx:key="id"
            wx:for-item="member"
            src="{{ossPrefixUrl}}{{member.avatar}}"
          ></image>
          <view class="more-members" wx:if="{{item.memberCount > item.recentMembers.length}}">
            +{{item.memberCount - item.recentMembers.length}}
          </view>
        </view>
        <view class="detail-link" wx:if="{{item.status !== 'pending'}}">查看详情</view>
        <view class="detail-link" wx:if="{{item.status === 'pending'}}">申请中...</view>
      </view>
    </view>
  </view>

  <!-- 加载更多提示 -->
  <view class="load-more" wx:if="{{groupList.length > 0}}">
    <view class="loading" wx:if="{{loading}}">
      <text class="loading-text">加载中...</text>
    </view>
    <view class="no-more" wx:elif="{{!hasMore}}">
      <text class="no-more-text">没有更多数据了</text>
    </view>
    <view class="pull-up" wx:else>
      <text class="pull-up-text">上拉加载更多</text>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{groupList.length === 0 && !loading}}">
    <view class="empty-icon">📝</view>
    <text class="empty-title">还没有加入任何小组</text>
    <text class="empty-subtitle">创建或加入一个小组开始打卡吧</text>
    <button class="empty-action" bindtap="joinGroup">加入小组</button>
  </view>
</view>
