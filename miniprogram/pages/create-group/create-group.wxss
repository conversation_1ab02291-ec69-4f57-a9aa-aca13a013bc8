/* create-group.wxss - 完全重写版本 */
.container {
  padding: 32rpx;
  background-color: #f6f6f6;
  min-height: 100vh;
}

.section-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 24rpx;
}

/* 图标选择 */
.icon-section {
  margin-bottom: 48rpx;
}

.icon-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 24rpx;
}

.icon-item {
  display: flex;
  justify-content: center;
  border: 4rpx solid transparent;
  border-radius: 16rpx;
  padding: 8rpx;
  transition: all 0.2s ease;
}

.icon-item.selected {
  border-color: #667eea;
  background: #f8faff;
}

.icon-display {
  width: 96rpx;
  height: 96rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
}

.icon-display.blue {
  background: #e3f2fd;
  color: #1976d2;
}

.icon-display.purple {
  background: #f3e5f5;
  color: #7b1fa2;
}

.icon-display.green {
  background: #e8f5e8;
  color: #388e3c;
}

.icon-display.yellow {
  background: #fff3e0;
  color: #f57c00;
}

.icon-display.red {
  background: #ffebee;
  color: #f44336;
}

.icon-display.indigo {
  background: #e8eaf6;
  color: #3f51b5;
}

.icon-display.pink {
  background: #fce4ec;
  color: #e91e63;
}

.icon-display.teal {
  background: #e0f2f1;
  color: #00695c;
}

.icon-display.orange {
  background: #fff3e0;
  color: #ef6c00;
}

.icon-display.gray {
  background: #f5f5f5;
  color: #757575;
}

/* 输入区域 */
.input-section {
  margin-bottom: 48rpx;
}

/* 修复：小组名称输入框 - placeholder垂直居中 */
.text-input {
  width: 100%;
  height: 96rpx;
  background: white;
  border-radius: 24rpx;
  padding: 0 32rpx;
  font-size: 32rpx;
  border: none;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);
  box-sizing: border-box;
  text-align: center;
  line-height: 96rpx;
  transition: all 0.3s ease;
}

.text-input:focus {
  box-shadow: 0 8rpx 40rpx rgba(102, 126, 234, 0.2);
  transform: translateY(-2rpx);
}

.textarea-input {
  width: 100%;
  min-height: 192rpx;
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  font-size: 28rpx;
  border: none;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.textarea-input:focus {
  box-shadow: 0 8rpx 40rpx rgba(102, 126, 234, 0.2);
  transform: translateY(-2rpx);
}

.input-count {
  display: block;
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 16rpx;
}

/* 设置卡片 - 重新设计 */
.settings-card,
.privacy-card {
  background: white;
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 48rpx;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);
}

.card-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 40rpx;
  position: relative;
}

.card-title::after {
  content: '';
  position: absolute;
  bottom: -20rpx;
  left: 0;
  width: 60rpx;
  height: 6rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 3rpx;
}

/* 修复：打卡次数设置项 - 防止文字换行 */
.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
  min-height: 80rpx;
}

.setting-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

/* 修复：设置标签不换行 */
.setting-label {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  flex-shrink: 0;
  margin-right: 24rpx;
}

.setting-info {
  flex: 1;
  min-width: 0;
}

.setting-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
  line-height: 1.4;
}

/* 旧样式已移除，使用新的毛玻璃按钮设计 */

/* 新增：垂直布局的设置项 */
.setting-item-vertical {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.setting-item-vertical:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

/* 新增：独立的标签样式 */
.setting-label-standalone {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  text-align: left;
  margin-bottom: 8rpx;
}

/* 新增：重新设计的计数器控制 - 配合毛玻璃效果 */
.counter-control-redesigned {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 32rpx;
  background: linear-gradient(135deg,
    rgba(102, 126, 234, 0.08) 0%,
    rgba(118, 75, 162, 0.06) 50%,
    rgba(102, 126, 234, 0.08) 100%);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  border-radius: 32rpx;
  padding: 32rpx;
  border: 2rpx solid rgba(102, 126, 234, 0.15);
  box-shadow:
    0 8rpx 32rpx rgba(102, 126, 234, 0.12),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.3);
}

/* 新增：毛玻璃效果的圆形按钮 - 强化样式优先级 */
.counter-btn-new {
  width: 88rpx !important;
  height: 88rpx !important;
  min-width: 88rpx !important;
  min-height: 88rpx !important;
  max-width: 88rpx !important;
  max-height: 88rpx !important;
  background: rgba(255, 255, 255, 0.25) !important;
  backdrop-filter: blur(20rpx) !important;
  -webkit-backdrop-filter: blur(20rpx) !important;
  border: 2rpx solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow:
    0 8rpx 32rpx rgba(102, 126, 234, 0.15),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.3) !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  overflow: hidden !important;
  flex-shrink: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
  box-sizing: border-box !important;
}

.counter-btn-new::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%) !important;
  border-radius: 50% !important;
  opacity: 0 !important;
  transition: opacity 0.3s ease !important;
}

.counter-btn-new::after {
  border: none !important;
  content: none !important;
}

.counter-btn-new:active {
  transform: scale(0.95) !important;
  background: rgba(255, 255, 255, 0.35) !important;
  box-shadow:
    0 4rpx 20rpx rgba(102, 126, 234, 0.25),
    inset 0 2rpx 8rpx rgba(255, 255, 255, 0.4) !important;
}

.counter-btn-new:active::before {
  opacity: 1 !important;
}

.counter-btn-new:disabled {
  background: rgba(200, 200, 200, 0.3) !important;
  backdrop-filter: blur(10rpx) !important;
  -webkit-backdrop-filter: blur(10rpx) !important;
  border: 2rpx solid rgba(200, 200, 200, 0.2) !important;
  box-shadow:
    0 2rpx 8rpx rgba(0, 0, 0, 0.1),
    inset 0 1rpx 2rpx rgba(255, 255, 255, 0.2) !important;
  transform: none !important;
}

.counter-btn-new:disabled::before {
  opacity: 0 !important;
}

/* 新增：按钮图标样式 - 适配毛玻璃效果 */
.counter-icon {
  font-size: 40rpx;
  font-weight: 400;
  color: #667eea;
  line-height: 1;
  z-index: 1;
  position: relative;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.counter-btn-new:disabled .counter-icon {
  color: #999;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.5);
}

/* 新增：计数器显示区域 */
.counter-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: white;
  border-radius: 24rpx;
  padding: 20rpx 32rpx;
  box-shadow: 0 6rpx 24rpx rgba(102, 126, 234, 0.1);
  border: 2rpx solid #e8eeff;
  min-width: 120rpx;
  position: relative;
}

.counter-value-new {
  font-size: 48rpx;
  font-weight: bold;
  color: #667eea;
  line-height: 1;
  margin-bottom: 4rpx;
}

/* 新增：可输入的计数器输入框 */
.counter-input {
  font-size: 48rpx !important;
  font-weight: bold !important;
  color: #667eea !important;
  line-height: 1 !important;
  margin-bottom: 4rpx !important;
  text-align: center !important;
  background: transparent !important;
  border: none !important;
  outline: none !important;
  width: 80rpx !important;
  min-width: 80rpx !important;
  padding: 0 !important;
  box-sizing: border-box !important;
  transition: all 0.3s ease !important;
}

.counter-input:focus {
  color: #5a67d8 !important;
  background: rgba(102, 126, 234, 0.05) !important;
  border-radius: 8rpx !important;
  padding: 4rpx 8rpx !important;
}

.counter-unit {
  font-size: 24rpx;
  color: #999;
  font-weight: 500;
  pointer-events: none;
}

/* 修复：提醒设置 - 去除switch白边框 */
.reminder-setting {
  background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
  border-radius: 20rpx;
  margin: 0 -16rpx;
  padding: 24rpx 16rpx;
  border: 2rpx solid #e8eeff;
}

.switch-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

/* 修复：去除switch白色边框 */
.reminder-switch {
  transform: scale(1.1);
  transition: all 0.3s ease;
}

/* 重要：去除微信小程序switch组件的默认边框 */
switch {
  border: none !important;
  outline: none !important;
}

switch::after {
  border: none !important;
}

switch::before {
  border: none !important;
}

/* 隐私设置 - 简化设计 */
.privacy-options {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.privacy-option {
  display: flex;
  align-items: flex-start;
  padding: 28rpx;
  border: 2rpx solid transparent;
  border-radius: 20rpx;
  background: #fafbff;
  transition: all 0.3s ease;
}

.privacy-option.selected {
  border-color: #667eea;
  background: #f8faff;
  box-shadow: 0 6rpx 24rpx rgba(102, 126, 234, 0.12);
}

.privacy-option radio {
  margin-right: 20rpx;
  margin-top: 2rpx;
  flex-shrink: 0;
}

.privacy-info {
  flex: 1;
  min-width: 0;
}

.privacy-title {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.privacy-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

/* 创建按钮 - 简化设计 */
.create-btn {
  width: 100%;
  padding: 36rpx 32rpx;
  border-radius: 24rpx;
  font-size: 36rpx;
  font-weight: 600;
  border: none;
  transition: all 0.3s ease;
  margin-top: 32rpx;
}

.create-btn::after {
  border: none;
}

.create-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 10rpx 40rpx rgba(102, 126, 234, 0.3);
}

.create-btn.active:active {
  transform: translateY(2rpx);
  box-shadow: 0 6rpx 24rpx rgba(102, 126, 234, 0.4);
}

.create-btn.disabled {
  background: #e8e8e8;
  color: #aaa;
  box-shadow: none;
  transform: none;
}
