// create-group.js
const { authManager, USER_ROLES } = require('../../utils/auth.js')
const { groupAPI } = require('../../utils/api.js')

const iconOptions = [
  { icon: '🏃', color: 'blue' },
  { icon: '📚', color: 'purple' },
  { icon: '💪', color: 'green' },
  { icon: '🎵', color: 'yellow' },
  { icon: '🎨', color: 'red' },
  { icon: '💻', color: 'indigo' },
  { icon: '❤️', color: 'pink' },
  { icon: '🌱', color: 'teal' },
  { icon: '📷', color: 'orange' },
  { icon: '➕', color: 'gray' }
]

Page({
  data: {
    iconOptions,
    selectedIcon: '🏃',
    selectedIconColor: 'blue',
    groupName: '',
    groupDescription: '',
    targetCount: 22,
    enableReminder: true,
    doPublic: true,
    creating: false,
    canCreate: false
  },

  onLoad() {
    this.checkPermission()
    this.checkCanCreate()
  },

  /**
   * 检查创建小组权限
   */
  checkPermission() {
    try {
      authManager.refreshUser()

      if (!authManager.canCreateGroup()) {
        wx.showModal({
          title: '权限不足',
          content: '您当前没有创建小组的权限，需要管理员权限',
          showCancel: false,
          success: () => {
            wx.navigateBack()
          }
        })
        return
      }
    } catch (error) {
      console.error('权限检查失败:', error)
      wx.showToast({
        title: '权限检查失败',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  // 选择图标
  selectIcon(e) {
    const { icon, color } = e.currentTarget.dataset
    this.setData({
      selectedIcon: icon,
      selectedIconColor: color
    })
  },

  // 小组名称输入
  onGroupNameInput(e) {
    this.setData({
      groupName: e.detail.value
    }, () => {
      this.checkCanCreate()
    })
  },

  // 小组描述输入
  onGroupDescriptionInput(e) {
    this.setData({
      groupDescription: e.detail.value
    })
  },

  // 减少目标次数
  decreaseTarget() {
    if (this.data.targetCount > 1) {
      this.setData({
        targetCount: this.data.targetCount - 1
      })
    }
  },

  // 增加目标次数
  increaseTarget() {
    if (this.data.targetCount < 31) {
      this.setData({
        targetCount: this.data.targetCount + 1
      })
    }
  },

  // 手动输入目标次数
  onTargetCountInput(e) {
    const value = parseInt(e.detail.value) || 1
    // 实时更新，但不做范围限制（在blur时处理）
    this.setData({
      targetCount: value
    })
  },

  // 输入框失去焦点时验证范围
  onTargetCountBlur(e) {
    let value = parseInt(e.detail.value) || 1

    // 限制范围在1-31之间
    if (value < 1) {
      value = 1
    } else if (value > 31) {
      value = 31
    }

    // 如果值发生了变化，更新显示
    if (value !== this.data.targetCount) {
      this.setData({
        targetCount: value
      })

      // 提示用户范围限制
      if (parseInt(e.detail.value) > 31) {
        wx.showToast({
          title: '最大值为31次',
          icon: 'none',
          duration: 1500
        })
      } else if (parseInt(e.detail.value) < 1) {
        wx.showToast({
          title: '最小值为1次',
          icon: 'none',
          duration: 1500
        })
      }
    }
  },

  // 提醒设置变化
  onReminderChange(e) {
    // 添加轻微延迟，避免页面闪动
    wx.nextTick(() => {
      this.setData({
        enableReminder: e.detail.value
      })
    })
  },

  // 设置隐私
  setPrivacy(e) {
    const doPublic = e.currentTarget.dataset.public
    this.setData({
      doPublic
    })
  },

  // 检查是否可以创建
  checkCanCreate() {
    const { groupName } = this.data
    const canCreate = groupName.trim().length > 0

    this.setData({
      canCreate
    })
  },

  // 创建小组
  async createGroup() {
    if (!this.data.canCreate || this.data.creating) {
      return
    }

    // 验证输入
    if (this.data.groupName.trim().length === 0) {
      wx.showToast({
        title: '请输入小组名称',
        icon: 'none'
      })
      return
    }

    this.setData({
      creating: true
    })

    try {
      // 构造新接口所需参数
      const createData = {
        name: this.data.groupName.trim(),
        description: this.data.groupDescription,
        doPublic: !!this.data.doPublic,
        metadata: {
          icon: this.data.selectedIcon,
          iconColor: this.data.selectedIconColor,
          enableReminder: this.data.enableReminder,
          targetCount: this.data.targetCount
        }
      }
      // 调用新接口
      const result = await groupAPI.createGroup(createData)

      wx.showToast({
        title: '小组创建成功！',
        icon: 'success'
      })

      setTimeout(() => {
        wx.redirectTo({
          url: `/pages/group-detail/group-detail?id=${result.id || result.groupId}`
        })
      }, 1500)

    } catch (error) {
      console.error('创建小组失败:', error)
      let errorMessage = '创建失败，请重试'
      if (error.code === 'CLUB1401') {
        errorMessage = '小组名称已存在，请换一个'
      } else if (error.code === 'CLUB1601') {
        errorMessage = '图片内容违规！'
      } else if (error.code === 'CLUB1602') {
        errorMessage = '文字内容违规！'
      }
      wx.showToast({
        title: errorMessage,
        icon: 'none'
      })
    } finally {
      this.setData({
        creating: false
      })
    }
  }
})
