<!--create-group.wxml-->
<view class="container">
  <!-- 小组图标选择 -->
  <view class="icon-section">
    <text class="section-label">选择小组图标</text>
    <view class="icon-grid">
      <view 
        class="icon-item {{selectedIcon === item.icon ? 'selected' : ''}}" 
        wx:for="{{iconOptions}}" 
        wx:key="icon"
        bindtap="selectIcon"
        data-icon="{{item.icon}}"
        data-color="{{item.color}}"
      >
        <view class="icon-display {{item.color}}">
          <text>{{item.icon}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 小组名称 -->
  <view class="input-section">
    <text class="section-label">小组名称 *</text>
    <input 
      class="text-input"
      placeholder="请输入小组名称"
      value="{{groupName}}"
      bindinput="onGroupNameInput"
      maxlength="20"
    />
    <text class="input-count">{{groupName.length}}/20</text>
  </view>

  <!-- 小组描述 -->
  <view class="input-section">
    <text class="section-label">小组描述</text>
    <textarea 
      class="textarea-input"
      placeholder="介绍一下这个小组的目标和规则..."
      value="{{groupDescription}}"
      bindinput="onGroupDescriptionInput"
      maxlength="100"
    ></textarea>
    <text class="input-count">{{groupDescription.length}}/100</text>
  </view>

  <!-- 小组设置 -->
  <view class="settings-card">
    <text class="card-title">打卡设置</text>

    <view class="setting-item-vertical">
      <text class="setting-label-standalone">每月最低打卡次数</text>
      <view class="counter-control-redesigned">
        <button class="counter-btn-new" bindtap="decreaseTarget" disabled="{{targetCount <= 1}}">
          <text class="counter-icon">−</text>
        </button>
        <view class="counter-display">
          <input
                  class="counter-input"
                  type="number"
                  value="{{targetCount}}"
                  bindinput="onTargetCountInput"
                  bindblur="onTargetCountBlur"
                  min="1"
                  max="31"
          />
          <text class="counter-unit">次</text>
        </view>
        <button class="counter-btn-new" bindtap="increaseTarget" disabled="{{targetCount >= 31}}">
          <text class="counter-icon">+</text>
        </button>
      </view>
    </view>

<!--    <view class="setting-item reminder-setting">-->
<!--      <view class="setting-info">-->
<!--        <text class="setting-label">开启每日提醒</text>-->
<!--        <text class="setting-desc">每天20:00提醒未打卡成员</text>-->
<!--      </view>-->
<!--      <view class="switch-container">-->
<!--        <switch-->
<!--          checked="{{enableReminder}}"-->
<!--          bindchange="onReminderChange"-->
<!--          color="#667eea"-->
<!--          class="reminder-switch"-->
<!--        />-->
<!--      </view>-->
<!--    </view>-->
  </view>

  <!-- 隐私设置 -->
  <view class="privacy-card">
    <text class="card-title">隐私设置</text>
    
    <view class="privacy-options">
      <view 
        class="privacy-option {{doPublic ? 'selected' : ''}}"
        bindtap="setPrivacy"
        data-public="{{true}}"
      >
        <radio checked="{{doPublic}}" color="#667eea"/>
        <view class="privacy-info">
          <text class="privacy-title">公开小组</text>
          <text class="privacy-desc">任何人都可以搜索并申请加入</text>
        </view>
      </view>
      
      <view 
        class="privacy-option {{!doPublic ? 'selected' : ''}}"
        bindtap="setPrivacy"
        data-public="{{false}}"
      >
        <radio checked="{{!doPublic}}" color="#667eea"/>
        <view class="privacy-info">
          <text class="privacy-title">私密小组</text>
          <text class="privacy-desc">仅通过邀请码加入</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 创建按钮 -->
  <button 
    class="create-btn {{canCreate ? 'active' : 'disabled'}}" 
    bindtap="createGroup"
    disabled="{{!canCreate || creating}}"
  >
    {{creating ? '创建中...' : '创建小组'}}
  </button>
</view>
