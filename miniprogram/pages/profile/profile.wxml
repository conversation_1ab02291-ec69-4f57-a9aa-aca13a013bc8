<!--profile.wxml-->
<loading show="{{pageLoading}}"></loading>
<view class="page-container">
  <!-- 测试内容 -->
  <view class="test-content">
    <text class="test-title">个人中心</text>
    <text class="test-subtitle">管理你的个人信息</text>
  </view>

  <!-- 用户信息卡片 -->
  <view class="user-card">
    <!-- 非编辑状态 -->
    <view class="user-info" wx:if="{{!isEditing}}">
      <image class="user-avatar" src="{{ossPrefixUrl}}{{userInfo.avatarUrl}}" mode="aspectFill" bindtap="startEdit"></image>
      <view class="user-details">
        <text class="user-name" bindtap="startEdit">{{userInfo.nickName}}</text>
        <text class="user-id">ID: {{userInfo.userId}}</text>
        <text class="user-role">身份: {{userRoleCN}}</text>
      </view>
      <view class="edit-btn" bindtap="startEdit">
        <text class="edit-icon">✏️</text>
      </view>
    </view>

    <!-- 编辑状态 -->
    <view class="edit-form" wx:if="{{isEditing}}">
      <view class="edit-avatar">
        <image class="user-avatar" src="{{editForm.avatarUrl}}" mode="aspectFill" bindtap="chooseAvatar"></image>
        <text class="avatar-tip">点击更换头像</text>
      </view>
      <view class="edit-fields">
        <view class="field-group">
          <text class="field-label">昵称 *</text>
          <input
            class="field-input"
            value="{{editForm.nickName}}"
            placeholder="请输入昵称"
            bindinput="onNickNameInput"
            maxlength="20"
          />
        </view>
        <view class="edit-actions">
          <button class="action-btn cancel" bindtap="cancelEdit">取消</button>
          <button class="action-btn save" bindtap="saveProfile">保存</button>
        </view>
      </view>
    </view>

    <!-- 用户统计 -->
    <view class="user-stats" wx:if="{{!isEditing}}">
      <view class="stat-item" bindtap="goToCheckinHistory">
        <text class="stat-number">{{userStats.totalCheckins}}</text>
        <text class="stat-label">总打卡</text>
      </view>
      <view class="stat-item" bindtap="goToMyGroups">
        <text class="stat-number">{{userStats.joinedGroups}}</text>
        <text class="stat-label">参与小组</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{userStats.maxConsecutive}}</text>
        <text class="stat-label">最长连续</text>
      </view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <view class="menu-item" bindtap="goToUserManage" wx:if="{{userInfo.role === 'ADMIN'}}">
      <view class="menu-icon red">
        <text>🧑‍💻</text>
      </view>
      <text class="menu-text">用户管理</text>
      <text class="menu-arrow">></text>
    </view>

    <view class="menu-item" bindtap="goToCheckinHistory">
      <view class="menu-icon blue">
        <text>📅</text>
      </view>
      <text class="menu-text">打卡记录</text>
      <text class="menu-arrow">></text>
    </view>

<!--    <view class="menu-item" bindtap="goToMyGroups">-->
<!--      <view class="menu-icon purple">-->
<!--        <text>👥</text>-->
<!--      </view>-->
<!--      <text class="menu-text">我的小组</text>-->
<!--      <text class="menu-arrow">></text>-->
<!--    </view>-->

<!--    <view class="menu-item" bindtap="goToAchievements">-->
<!--      <view class="menu-icon green">-->
<!--        <text>🏆</text>-->
<!--      </view>-->
<!--      <text class="menu-text">我的成就</text>-->
<!--      <text class="menu-arrow">></text>-->
<!--    </view>-->

<!--    <view class="menu-item" bindtap="goToNotifications">-->
<!--      <view class="menu-icon yellow">-->
<!--        <text>🔔</text>-->
<!--      </view>-->
<!--      <view class="menu-content">-->
<!--        <text class="menu-text">消息通知</text>-->
<!--        <view class="notification-badge" wx:if="{{unreadCount > 0}}">-->
<!--          <text>{{unreadCount}}</text>-->
<!--        </view>-->
<!--      </view>-->
<!--      <text class="menu-arrow">></text>-->
<!--    </view>-->
  </view>

   <!-- 设置选项 -->
<!--   <view class="settings-section">-->
<!--    <text class="section-title">设置</text>-->
<!--    -->
<!--    <view class="setting-item">-->
<!--      <view class="setting-info">-->
<!--        <text class="setting-icon">🔔</text>-->
<!--        <text class="setting-text">推送通知</text>-->
<!--      </view>-->
<!--      <switch -->
<!--        checked="{{settings.pushNotification}}" -->
<!--        bindchange="onPushNotificationChange"-->
<!--        color="#667eea"-->
<!--      />-->
<!--    </view>-->

<!--    <view class="setting-item">-->
<!--      <view class="setting-info">-->
<!--        <text class="setting-icon">🌙</text>-->
<!--        <text class="setting-text">夜间模式</text>-->
<!--      </view>-->
<!--      <switch -->
<!--        checked="{{settings.darkMode}}" -->
<!--        bindchange="onDarkModeChange"-->
<!--        color="#667eea"-->
<!--      />-->
<!--    </view>-->

<!--    <view class="setting-item" bindtap="goToPrivacySettings">-->
<!--      <view class="setting-info">-->
<!--        <text class="setting-icon">🛡️</text>-->
<!--        <text class="setting-text">隐私设置</text>-->
<!--      </view>-->
<!--      <text class="menu-arrow">></text>-->
<!--    </view>-->

<!--    <view class="setting-item" bindtap="goToHelp">-->
<!--      <view class="setting-info">-->
<!--        <text class="setting-icon">❓</text>-->
<!--        <text class="setting-text">帮助与反馈</text>-->
<!--      </view>-->
<!--      <text class="menu-arrow">></text>-->
<!--    </view>-->

<!--    <view class="setting-item" bindtap="goToAbout">-->
<!--      <view class="setting-info">-->
<!--        <text class="setting-icon">ℹ️</text>-->
<!--        <text class="setting-text">关于我们</text>-->
<!--      </view>-->
<!--      <text class="menu-arrow">></text>-->
<!--    </view>-->
<!--  </view>-->

  <!-- 退出登录 -->
<!--  <button class="logout-btn" bindtap="logout">-->
<!--    退出登录-->
<!--  </button>-->
</view>
