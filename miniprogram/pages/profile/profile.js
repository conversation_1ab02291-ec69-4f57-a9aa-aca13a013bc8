// profile.js
const { authManager, USER_ROLES, USER_ROLES_CN } = require('../../utils/auth.js')
const { userAPI } = require('../../utils/api.js')
const { uploadManager } = require('../../utils/upload.js')
const config = require("../../utils/config");

const defaultAvatarUrl = 'https://minio.spaceink.top/community-hub/2025-07-13/default/1752408116245-49e807-4wqf2A.jpeg'

const OSS_PREFIX_URL = config.ossPrefixUrl

Page({
  data: {
    userInfo: {
      avatarUrl: defaultAvatarUrl,
      nickName: '未设置',
      userId: '123456',
      joinDate: '2024.01.15'
    },
    userStats: {
      totalCheckins: 0,
      joinedGroups: 0,
      maxConsecutive: 0
    },
    unreadCount: 0,
    settings: {
      pushNotification: true,
      darkMode: false
    },
    // 编辑相关状态
    isEditing: false,
    editForm: {
      nickName: '',
      avatarUrl: ''
    },
    userRole: USER_ROLES.GUEST,
    userRoleCN: USER_ROLES_CN.get(USER_ROLES.GUEST),
    ossPrefixUrl: config.ossPrefixUrl,
    pageLoading: true,
  },

  onLoad() {
    // a empty implementation
  },

  async onShow() {
    if (wx.getStorageSync('profileEdit')) {
      this.startEdit();
      wx.removeStorageSync('profileEdit');
    }

    this.setData({ pageLoading: true });
    try {
      this.checkUserRole();
      await Promise.all([
        this.loadUserInfo(),
        this.loadUserStats(),
        this.loadSettings()
      ]);
    } finally {
      this.setData({ pageLoading: false });
    }
  },

  /**
   * 检查用户角色
   */
  checkUserRole() {
    const userRole = authManager.getCurrentRole()
    this.setData({
      userRole: userRole,
      userRoleCN: USER_ROLES_CN.get(userRole)
    })
  },

  // 加载用户信息
  async loadUserInfo() {
    try {
      // 优先从API获取最新用户信息
      const userInfo = await userAPI.getUserInfo()
      console.log('从API获取用户信息:', userInfo)

      this.setData({
        userInfo: {
          role: userInfo.role,
          avatarUrl: userInfo.avatar || defaultAvatarUrl,
          nickName: userInfo.nickName || userInfo.name || '未设置',
          userId: userInfo.userId || userInfo.id,
          joinDate: userInfo.joinDate || '未知'
        }
      })
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 如果API失败，从本地存储获取
      const localUserInfo = wx.getStorageSync('userInfo')
      if (localUserInfo) {
        this.setData({
          userInfo: {
            ...this.data.userInfo,
            ...localUserInfo
          }
        })
      }
    }
  },

  // 加载用户统计数据
  async loadUserStats() {
    const attendInfo = await userAPI.getProfileAttendInfo();

    this.setData({
      userStats: {
        totalCheckins: attendInfo.totalCount,
        joinedGroups: attendInfo.joinGroupCount,
        maxConsecutive: attendInfo.longestCount,
      }
    })
  },

  // 加载设置
  loadSettings() {
    const settings = wx.getStorageSync('settings') || {}
    this.setData({
      settings: {
        ...this.data.settings,
        ...settings
      }
    })
  },

  // 推送通知设置变化
  onPushNotificationChange(e) {
    const value = e.detail.value
    this.setData({
      'settings.pushNotification': value
    })

    // 保存到本地存储
    const settings = wx.getStorageSync('settings') || {}
    settings.pushNotification = value
    wx.setStorageSync('settings', settings)
  },

  // 夜间模式设置变化
  onDarkModeChange(e) {
    const value = e.detail.value
    this.setData({
      'settings.darkMode': value
    })

    // 保存到本地存储
    const settings = wx.getStorageSync('settings') || {}
    settings.darkMode = value
    wx.setStorageSync('settings', settings)
  },

  // 页面跳转方法
  goToCheckinHistory() {
    wx.navigateTo({
      url: '/pages/checkin-history/checkin-history'
    })
  },

  goToAchievements() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  goToMyGroups() {
    wx.switchTab({
      url: '/pages/groups/groups'
    })
  },

  goToNotifications() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
    // wx.navigateTo({
    //   url: '/pages/notifications/notifications'
    // })
  },

  goToPrivacySettings() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  goToHelp() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  goToAbout() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },
 
  goToUserManage() {
    wx.navigateTo({
      url: '/pages/user-manage/user-manage'
    })
  },

   // 退出登录
   logout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除本地存储的用户数据
          wx.removeStorageSync('userInfo')
          wx.removeStorageSync('settings')

          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          })

          // 重新启动小程序
          setTimeout(() => {
            wx.reLaunch({
              url: '/pages/home/<USER>'
            })
          }, 1500)
        }
      }
    })
  },

  /**
   * 编辑个人信息相关方法
   */

  // 开始编辑
  startEdit() {
    this.setData({
      isEditing: true,
      editForm: {
        nickName: this.data.userInfo.nickName,
        avatarUrl: OSS_PREFIX_URL + this.data.userInfo.avatarUrl
      }
    })
  },

  // 取消编辑
  cancelEdit() {
    this.setData({
      isEditing: false,
      editForm: {
        nickName: '',
        avatarUrl: ''
      }
    })
  },

  // 昵称输入
  onNickNameInput(e) {
    this.setData({
      'editForm.nickName': e.detail.value
    })
  },

  // 选择头像
  chooseAvatar() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        if (res.tempFiles[0].size > config.upload.maxSize) {
          wx.showToast({
            title: '图片大小不能超过' + config.upload.size,
            icon: 'none'
          })
          return
        }
        const tempFilePath = res.tempFiles[0].tempFilePath
        this.setData({
          'editForm.avatarUrl': tempFilePath
        })
      },
      fail: (error) => {
        console.error('选择头像失败:', error)
        wx.showToast({
          title: '选择头像失败',
          icon: 'none'
        })
      }
    })
  },

  // 保存个人信息
  async saveProfile() {
    const { nickName, avatarUrl } = this.data.editForm

    // 验证昵称
    if (!nickName || nickName.trim() === '') {
      wx.showToast({
        title: '请输入昵称',
        icon: 'none'
      })
      return
    }

    try {
      wx.showLoading({
        title: '保存中...'
      })

      let finalAvatarUrl = avatarUrl

      // 如果选择了新头像，先上传
      if (avatarUrl && (avatarUrl.startsWith('http://tmp/') || avatarUrl.startsWith('wxfile://'))) {
        try {
          wx.hideLoading() // 隐藏保存中的loading，显示上传进度
          const uploadResult = await uploadManager.uploadAvatar(avatarUrl)
          finalAvatarUrl = uploadResult.url
          console.log('头像上传成功:', finalAvatarUrl)

          // 重新显示保存中的loading
          wx.showLoading({
            title: '保存中...'
          })
        } catch (uploadError) {
          console.error('头像上传失败:', uploadError)
          wx.hideLoading()
          // uploadManager已经显示了错误提示，这里不需要重复显示
          return
        }
      }

      // 调用更新用户信息接口
      const updateData = {
        name: nickName.trim(),
        wxUser: {
          nickName: nickName.trim(),
          avatar: finalAvatarUrl.replace(config.ossPrefixUrl, '')
        }
      }

      await userAPI.updateUserProfile(updateData)

      wx.hideLoading()
      wx.showToast({
        title: '保存成功',
        icon: 'success'
      })

      // 更新本地数据
      this.setData({
        isEditing: false,
        'userInfo.nickName': nickName.trim(),
        'userInfo.avatarUrl': finalAvatarUrl.replace(config.ossPrefixUrl, '')
      })

      await authManager.reloadAndRefreshUserInfo()
      this.checkUserRole()
      await this.loadUserStats();

    } catch (error) {
      wx.hideLoading()
      console.error('保存个人信息失败:', error)
      let errorMessage = '保存失败'
      if (error.code === 'CLUB1601') {
        errorMessage = '图片内容违规！'
      } else if (error.code === 'CLUB1602') {
        errorMessage = '文字内容违规！'
      }
      wx.showToast({
        title: errorMessage,
        icon: 'none'
      })
    }
  },
})
