<!--miniprogram/pages/user-manage/user-manage.wxml-->
<view class="page-container">
  <!-- 用户列表 -->
  <view class="user-list">
    <view class="user-card" wx:for="{{users}}" wx:key="id">
      <view class="user-card-header">
        <image class="avatar" src="{{item.wxUser.avatar ? ossPrefixUrl + item.wxUser.avatar : defaultAvatarUrl}}"></image>
        <view class="user-info">
          <text class="nickname">{{item.wxUser.nickName || '未设置昵称'}}</text>
          <text class="user-id">ID: {{item.id}}</text>
        </view>
        <view class="user-role-badge {{item.role}}">{{item.roleText}}</view>
      </view>
      <view class="user-card-actions">
        <button class="action-btn secondary" bindtap="onEdit" data-user="{{item}}">编辑</button>
        <button
          class="action-btn primary {{item.id === currentUser.userId ? 'disabled' : ''}}"
          bindtap="onAuthorize"
          data-user="{{item}}">
          授权
        </button>
      </view>
    </view>
  </view>

  <!-- 加载提示 -->
  <view class="load-more-container">
    <text wx:if="{{loading}}">加载中...</text>
    <text wx:if="{{!hasMore && users.length > 0}}">没有更多数据了</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{users.length === 0 && !loading}}">
    <text class="empty-icon">🤷</text>
    <text class="empty-text">暂无用户信息</text>
  </view>

  <!-- 编辑用户弹窗 -->
  <view class="modal-mask" wx:if="{{editModal.show}}" bindtap="closeEditModal"></view>
  <view class="modal-dialog" wx:if="{{editModal.show}}">
    <view class="modal-header">
      <text class="modal-title">编辑用户</text>
    </view>
    <view class="modal-body">
      <view class="edit-avatar-section">
        <image class="edit-avatar" src="{{editModal.form.avatarUrl}}" bindtap="chooseAvatar"></image>
        <text class="edit-avatar-tip">点击更换头像</text>
      </view>
      <view class="form-item">
        <text class="form-label">昵称</text>
        <input class="form-input" value="{{editModal.form.nickName}}" bindinput="onNickNameInput" placeholder="请输入用户昵称" />
      </view>
    </view>
    <view class="modal-footer">
      <button class="modal-btn cancel" bindtap="closeEditModal">取消</button>
      <button class="modal-btn confirm" bindtap="saveUser">保存</button>
    </view>
  </view>
</view>