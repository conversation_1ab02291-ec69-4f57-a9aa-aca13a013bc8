/* miniprogram/pages/user-manage/user-manage.wxss */
.page-container {
  padding: 24rpx;
  background-color: #f7f8fa;
  min-height: 100vh;
}

.user-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.user-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.05);
}

.user-card-header {
  display: flex;
  align-items: center;
  padding-bottom: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.avatar {
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  margin-right: 24rpx;
}

.user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.nickname {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.user-id {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

.user-role-badge {
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: #fff;
}
.user-role-badge.ADMIN { background-color: #f56c6c; }
.user-role-badge.MANAGER { background-color: #e6a23c; }
.user-role-badge.USER { background-color: #409eff; }
.user-role-badge.GUEST { background-color: #8eb4da; }

.user-card-actions {
  display: flex;
  justify-content: flex-end;
  gap: 24rpx;
  padding-top: 24rpx;
}

.action-btn {
  padding: 12rpx 32rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  line-height: 1.5;
}
.action-btn.primary {
  background-color: #667eea;
  color: #fff;
}
.action-btn.secondary {
  background-color: #fff;
  color: #667eea;
  border: 1rpx solid #667eea;
}

.action-btn.disabled {
  opacity: 0.6;
  pointer-events: none;
}

/* 加载和空状态 */
.load-more-container, .empty-state {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
}
.empty-icon { font-size: 80rpx; }
.empty-text { display: block; margin-top: 16rpx; }

/* 编辑弹窗样式 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.modal-dialog {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  background-color: #fff;
  border-radius: 16rpx;
  z-index: 1001;
  padding: 40rpx;
}

.modal-header {
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 40rpx;
}

.edit-avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
}

.edit-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-bottom: 16rpx;
}

.edit-avatar-tip {
  font-size: 24rpx;
  color: #999;
}

.form-item {
  margin-bottom: 32rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  margin-bottom: 16rpx;
}

.form-input {
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 28rpx;
}

.modal-footer {
  display: flex;
  gap: 24rpx;
  margin-top: 40rpx;
}

.modal-btn {
  flex: 1;
}
.modal-btn.cancel {
  background-color: #f0f0f0;
  color: #333;
}
.modal-btn.confirm {
  background-color: #667eea;
  color: #fff;
}