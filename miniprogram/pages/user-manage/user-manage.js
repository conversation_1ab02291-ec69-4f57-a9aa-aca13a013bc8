// miniprogram/pages/user-manage/user-manage.js
const { userAPI } = require('../../utils/api.js');
const { authManager, USER_ROLES, USER_ROLES_CN } = require('../../utils/auth.js');
const { uploadManager } = require('../../utils/upload.js');
const config = require("../../utils/config");

const defaultAvatarUrl = 'https://minio.spaceink.top/community-hub/2025-07-13/default/1752408116245-49e807-4wqf2A.jpeg';

function getKeyByValue(map, value) {
  for (const [key, val] of map.entries()) {
    if (val === value) {
      return key;
    }
  }
  return undefined; // 没有找到时返回 undefined
}

Page({
  data: {
    users: [],
    page: 1,
    size: 10,
    totalPages: 0,
    loading: false,
    hasMore: true,
    ossPrefixUrl: config.ossPrefixUrl,
    defaultAvatarUrl: defaultAvatarUrl,
    userRoleCN: USER_ROLES_CN,
    currentUser: null,
    editModal: {
      show: false,
      form: {
        id: null,
        nickName: '',
        avatarUrl: '',
      },
      originalAvatar: '', // 用于判断头像是否被修改
    },
  },

  onLoad(options) {
    authManager.init();
    this.setData({
      currentUser: authManager.userInfo,
    });
    this.loadUsers(true);
  },

  async loadUsers(refresh = false) {
    if (this.data.loading) return;
    this.setData({ loading: true });

    let pageNum = refresh ? 1 : this.data.page;

    try {
      const result = await userAPI.getUserList(pageNum, this.data.size);
      const { content = [], page } = result;

      // Pre-process roles into displayable text
      const processedUsers = content.map(user => {
        return {
          ...user,
          roleText: USER_ROLES_CN.get(user.role) || user.role
        };
      });
      this.setData({
        users: refresh ? processedUsers : this.data.users.concat(processedUsers),
        totalPages: page.totalPages,
        page: page.number + 2, // API pageNum is 1-based, response `number` is 0-based. So next pageNum is number + 2.
        hasMore: (page.number + 1) < page.totalPages,
      });
    } catch (error) {
      console.error("Failed to load users:", error);
      wx.showToast({ title: '加载用户失败', icon: 'none' });
    } finally {
      this.setData({ loading: false });
      wx.stopPullDownRefresh();
    }
  },

  onPullDownRefresh() {
    this.loadUsers(true);
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadUsers();
    }
  },

  onEdit(event) {
    const user = event.currentTarget.dataset.user;
    this.setData({
      'editModal.show': true,
      'editModal.form.id': user.id,
      'editModal.form.nickName': user.wxUser.nickName,
      'editModal.form.avatarUrl': user.wxUser.avatar ? this.data.ossPrefixUrl + user.wxUser.avatar : defaultAvatarUrl,
      'editModal.originalAvatar': user.wxUser.avatar ? this.data.ossPrefixUrl + user.wxUser.avatar : defaultAvatarUrl,
    });
  },

  closeEditModal() {
    this.setData({ 'editModal.show': false });
  },

  onNickNameInput(e) {
    this.setData({
      'editModal.form.nickName': e.detail.value,
    });
  },

  chooseAvatar() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        this.setData({
          'editModal.form.avatarUrl': res.tempFiles[0].tempFilePath,
        });
      },
    });
  },

  async saveUser() {
    const { id, nickName, avatarUrl } = this.data.editModal.form;

    if (!nickName || nickName.trim() === '') {
      wx.showToast({ title: '昵称不能为空', icon: 'none' });
      return;
    }

    wx.showLoading({ title: '保存中...' });

    try {
      let finalAvatarPath = this.data.editModal.originalAvatar.replace(this.data.ossPrefixUrl, '');

      // 如果头像被修改过，则上传新头像
      if (avatarUrl !== this.data.editModal.originalAvatar) {
        const uploadResult = await uploadManager.uploadAvatar(avatarUrl);
        finalAvatarPath = uploadResult.url.replace(this.data.ossPrefixUrl, '');
      }

      const updateData = {
        id: id,
        name: nickName.trim(),
        wxUser: {
          nickName: nickName.trim(),
          avatar: finalAvatarPath,
        },
      };

      await userAPI.updateUserProfile(updateData);
      wx.hideLoading();
      wx.showToast({ title: '保存成功', icon: 'success' });
      this.closeEditModal();
      this.loadUsers(true); // 刷新列表

    } catch (error) {
      wx.hideLoading();
      console.error('保存用户信息失败:', error);
      wx.showToast({ title: '保存失败，请重试', icon: 'none' });
    }
  },

  onAuthorize(event) {
    const user = event.currentTarget.dataset.user;
    if (user.id === this.data.currentUser.id) {
      return;
    }

    const itemList = USER_ROLES_CN.values().toArray().filter(role => role !== '访客');

    wx.showActionSheet({
      itemList: itemList,
      success: async (res) => {
        const newRole = itemList[res.tapIndex];
        try {
          await userAPI.assignUserRole(user.id, getKeyByValue(USER_ROLES_CN, newRole));
          wx.showToast({ title: '授权成功' });
          this.loadUsers(true); // 刷新列表
        } catch (error) {
          console.error("Failed to assign role:", error);
          wx.showToast({ title: '授权失败', icon: 'none' });
        }
      },
    });
  },
});