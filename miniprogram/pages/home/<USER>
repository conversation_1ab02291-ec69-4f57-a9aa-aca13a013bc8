<!--home.wxml-->
<loading show="{{pageLoading}}"></loading>
<view class="page-container">
  <!-- 测试内容 -->
  <view class="test-content">
    <text class="test-title">打卡小程序</text>
    <text class="test-subtitle">欢迎使用！</text>
  </view>

  <!-- 头部问候 -->
  <view class="greeting">
    <text class="greeting-title">{{greetingText}}</text>
    <text class="greeting-subtitle">今天也要坚持打卡哦 💪</text>
  </view>

  <!-- 今日打卡状态卡片 -->
  <view class="checkin-card">
    <view class="checkin-header">
      <view class="checkin-info">
        <text class="checkin-title">今日打卡</text>
        <text class="checkin-date">{{todayDate}}</text>
        <!-- 显示用户角色 -->
        <text class="user-role" wx:if="{{userRole === 'GUEST'}}">当前身份：访客</text>
      </view>
      <view class="checkin-icon">
        <text class="iconfont">⏳</text>
      </view>
    </view>
    <!-- 根据权限显示打卡按钮 -->
    <button
      class="checkin-btn"
      bindtap="goToCheckin"
      disabled="{{!canCheckin}}"
      wx:if="{{canCheckin}}"
    >
      立即打卡
    </button>
    <!-- 权限不足提示 -->
    <view class="permission-tip"
          bindtap="permissionFill"
          wx:if="{{!canCheckin && userRole === 'GUEST'}}">
      <text class="tip-text">完善个人信息后可打卡</text>
    </view>
    <!-- 权限不足提示 -->
    <view class="permission-tip" wx:if="{{!canCheckin && userRole !== 'GUEST'}}">
      <text class="tip-text">暂无打卡权限</text>
    </view>
  </view>

  <!-- 我的小组 -->
  <view class="my-groups">
    <view class="section-header">
      <text class="section-title">我的小组</text>
      <text class="section-more" bindtap="goToGroups">查看全部</text>
    </view>
    
    <!-- 局部loading -->
    <view class="groups-loading" wx:if="{{groupsLoading}}">
      <text class="loading-text">加载中...</text>
    </view>
    <view class="groups-list" wx:elif="{{!groupsLoading}}">
      <view class="group-item" wx:for="{{myGroups}}" wx:key="id" bindtap="goToGroupDetail" data-id="{{item.id}}" data-status="{{item.status}}">
        <view class="group-content">
          <view class="group-icon {{item.iconColor}}">
            <text class="iconfont">{{item.icon}}</text>
          </view>
          <view class="group-info">
            <text class="group-name">{{item.name}}</text>
            <text class="group-progress">本月 {{item.currentCount}}/{{item.targetCount}} 次</text>
          </view>
        </view>
        <view class="group-status {{item.status}}" wx:if="{{item.status !== 'pending'}}">
          <text class="iconfont">{{item.statusIcon}}</text>
        </view>
        <view class="group-pending" wx:if="{{item.status === 'pending'}}">
          <text class="iconfont">申请中...</text>
        </view>
      </view>
    </view>
  </view>
</view>
