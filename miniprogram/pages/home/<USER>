// home.js
const { formatTime } = require('../../utils/util')
const { authManager, USER_ROLES } = require('../../utils/auth.js')
const { groupAPI } = require('../../utils/api.js')



Page({
  data: {
    greetingText: '早上好！',
    todayDate: '2024年6月28日',
    myGroups: [],
    // 权限相关数据
    userRole: USER_ROLES.GUEST,
    canCheckin: false,
    canJoinGroup: false,
    canCreateGroup: false,
    // 新增局部loading状态
    groupsLoading: false,
    pageLoading: true,
  },

  async onLoad() {
    console.log('首页加载中...')
    this.setData({ pageLoading: true });
    await this.initPage();
    this.setData({ pageLoading: false });
  },

  onReady() {
    console.log('首页渲染完成')
  },

  async onShow() {
    console.log('首页显示');
    this.setData({ pageLoading: true });
    try {
      this.checkUserRole();
      // 刷新小组数据
      await this.loadMyGroups();
    } finally {
      this.setData({ pageLoading: false });
    }
  },

  /**
   * 检查用户角色和权限
   */
  checkUserRole() {
    const userRole = authManager.getCurrentRole()
    const canCheckin = authManager.canCheckin()
    const canJoinGroup = authManager.canJoinGroup()
    const canCreateGroup = authManager.canCreateGroup()

    console.log('用户角色:', userRole)
    console.log('权限检查:', { canCheckin, canJoinGroup, canCreateGroup })

    this.setData({
      userRole,
      canCheckin,
      canJoinGroup,
      canCreateGroup
    })
  },

  async initPage() {
    console.log('初始化页面数据')
    this.setGreeting()
    this.setTodayDate()
    console.log('页面数据设置完成', this.data)
  },

  /**
   * 加载我的小组列表（首页只显示前3个）
   */
  async loadMyGroups() {
    this.setData({ groupsLoading: true })
    try {
      const response = await groupAPI.getUserGroups(1, 3)
      const groups = this.transformGroupsData(response.content || [])
      this.setData({
        myGroups: groups
      })
      console.log('首页小组列表加载成功:', groups)
    } catch (error) {
      console.error('加载小组列表失败:', error)
      // 如果加载失败，使用空数组，不显示错误提示（首页体验优先）
      this.setData({
        myGroups: []
      })
    } finally {
      this.setData({ groupsLoading: false })
    }
  },

  /**
   * 转换接口数据为页面所需格式
   */
  transformGroupsData(apiGroups) {
    return apiGroups.map(item => {
      const group = item.group
      const metadata = group.metadata || {}

      // 计算进度状态
      let status = 'waiting'
      let statusIcon = '⏰'

      if (item.status === 'PENDING') {
        status = 'pending'
        statusIcon = '申请中...'
      } else if (item.attendedToday) {
        status = 'completed'
        statusIcon = '✔️'
      }

      return {
        id: group.id,
        name: group.name || '未命名小组',
        icon: metadata.icon || '📝',
        iconColor: metadata.iconColor || 'blue',
        currentCount: item.currentCount || 0,
        targetCount: metadata.targetCount || 30,
        status: status,
        statusIcon: statusIcon
      }
    })
  },

  setGreeting() {
    const hour = new Date().getHours()
    let greeting = '早上好！'

    if (hour >= 6 && hour < 12) {
      greeting = '早上好！'
    } else if (hour >= 12 && hour < 18) {
      greeting = '下午好！'
    } else {
      greeting = '晚上好！'
    }

    this.setData({
      greetingText: greeting
    })
  },

  setTodayDate() {
    const today = new Date()
    const year = today.getFullYear()
    const month = today.getMonth() + 1
    const day = today.getDate()

    this.setData({
      todayDate: `${year}年${month}月${day}日`
    })
  },



  // 页面跳转方法
  goToCheckin() {
    // 检查权限
    if (!this.data.canCheckin) {
      authManager.showPermissionDenied('打卡功能')
      return
    }

    wx.navigateTo({
      url: '/pages/checkin/checkin'
    })
  },

  goToGroups() {
    wx.switchTab({
      url: '/pages/groups/groups'
    })
  },

  goToGroupDetail(e) {
    const status = e.currentTarget.dataset.status
    if (status === 'pending') {
      wx.showToast({
        title: '管理员全速审核中...',
        icon: 'none'
      })
      return;
    }

    const groupId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/group-detail/group-detail?id=${groupId}`
    })
  },

  // 跳转到完善个人信息
  permissionFill() {
    wx.setStorageSync('profileEdit', true);
    wx.switchTab({
      url: `/pages/profile/profile`
    })
  }

})
