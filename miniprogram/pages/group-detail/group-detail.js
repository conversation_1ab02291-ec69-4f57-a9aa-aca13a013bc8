// group-detail.js

const { groupAPI } = require('../../utils/api.js')
const config = require("../../utils/config");
const {USER_ROLES_CN} = require('../../utils/auth.js')

Page({
  data: {
    groupId: '',
    groupInfo: {},
    monthStats: {},
    memberList: [],
    recentActivities: [], // 保持原有，但实际数据将由 attendRecords 提供
    ossPrefixUrl: config.ossPrefixUrl,

    // 小组打卡记录分页相关
    attendRecords: [],
    attendRecordsPage: 0, // 当前页码，从0开始
    attendRecordsSize: 10, // 每页数量
    attendRecordsTotalPages: 0, // 总页数
    isAttendRecordsLoading: false, // 是否正在加载
    hasMoreAttendRecords: true, // 是否还有更多数据

    // 打卡详情弹窗相关
    showCheckinDetailModal: false,
    currentCheckinDetail: null,
    pageLoading: true,
  },

  onLoad() {
    // a empty implementation
  },

  onShow() {
    // 获取当前小程序的页面栈
    let pages = getCurrentPages();
    // 数组中索引最大的页面--当前页面
    const options = pages[pages.length-1].options;
    // 当页面显示时，重新加载打卡记录以确保最新数据
    if (options.id) {
      this.setData({
        groupId: options.id,
        loaded: true,
      })
      this.loadGroupDetail(options.id)
      this.loadAttendRecords(options.id, true)
    } else if (this.data.groupId) {
      this.loadGroupDetail(this.data.groupId)
      this.loadAttendRecords(this.data.groupId, true);
    }
  },

  /**
   * 加载小组打卡记录
   * @param {string} groupId 小组ID
   * @param {boolean} reset 是否重置分页
   */
  async loadAttendRecords(groupId, reset = false) {
    if (this.data.isAttendRecordsLoading || (!this.data.hasMoreAttendRecords && !reset)) {
      return
    }

    this.setData({ isAttendRecordsLoading: true })

    try {
      let currentPage = reset ? 0 : this.data.attendRecordsPage
      const res = await groupAPI.getGroupAttendRecords(
        groupId,
        currentPage,
        this.data.attendRecordsSize
      )
      const newRecords = res.content.map(record => ({
        id: record.id,
        memberAvatar: (record.member && record.member.user && record.member.user.wxUser && record.member.user.wxUser.avatar) || '',
        memberName: (record.member && record.member.user && record.member.user.name) || '未知成员',
        attendanceTime: record.attendanceTime,
        comment: record.comment,
        displayComment: record.comment && record.comment.length > 10 ? record.comment.substring(0, 10) + '...' : record.comment,
        assetUrl: record.assetUrl,
      }))

      let updatedRecords = reset ? newRecords : this.data.attendRecords.concat(newRecords)
      let hasMore = res.page.number < res.page.totalPages - 1

      this.setData({
        attendRecords: updatedRecords,
        attendRecordsPage: currentPage + 1,
        attendRecordsTotalPages: res.page.totalPages,
        hasMoreAttendRecords: hasMore,
      })
    } catch (error) {
      console.error('加载打卡记录失败', error)
      wx.showToast({
        title: '加载打卡记录失败',
        icon: 'none'
      })
      this.setData({ hasMoreAttendRecords: false }) // 加载失败则认为没有更多数据
    } finally {
      this.setData({ isAttendRecordsLoading: false })
    }
  },

  // 页面触底事件处理
  onReachBottom() {
    if (this.data.hasMoreAttendRecords) {
      this.loadAttendRecords(this.data.groupId)
    } else {
      // 不再显示全屏toast，而是在页面底部显示提示
    }
  },

  // 加载小组详情（优先用真实接口数据）
  async loadGroupDetail(groupId) {
    try {
      const res = await groupAPI.getGroupDetail(groupId)
      // 数据结构适配
      const group = res.group || {}
      const metadata = group.metadata || {}
      const groupMembers = group.groupMembers || []
      const monthStats = {
        targetDays: metadata.targetCount || 0,
        completedDays: res.currentCount || 0,
        completionRate: metadata.targetCount ? Math.round((res.currentCount / metadata.targetCount) * 100) : 0
      }
      const memberList = groupMembers.map(m => ({ // Removed slice(0, 5) to show all members
        id: (m.user && m.user.id) || Math.random(),
        name: (m.user && m.user.name) || '成员',
        avatar: (m.user && m.user.wxUser && m.user.wxUser.avatar) || '',
        role: m.role || 'USER', // Default to USER if not provided
        roleCN: USER_ROLES_CN.get(m.role) || '会员',
        isNew: m.daysSinceJoin <= 31 ? 1 : 0,
        currentCount: m.currentCount || 0,
        targetCount: metadata.targetCount || 0,
        status: m.status, // Changed to ACTIVE/PENDING for consistency with API
        statusIcon: m.status === 'PENDING' ? '➕' : m.attendedToday ? '✔️' : '⏰',
        x: 0, // For movable-view swipe
        buttonsWidth: 360 // Width of the two action buttons combined
      }))
      this.setData({
        attendedToday: res.attendedToday,
        attendedTip: res.attendedToday ? '✔️今日已打卡' : '✨ 立即打卡',
        groupInfo: {
          id: group.id,
          name: group.name || '未命名小组',
          inviteCode: group.inviteCode,
          icon: metadata.icon || '📝',
          iconColor: metadata.iconColor || 'blue',
          memberCount: groupMembers.length,
          role: res.role || 'USER',
          roleCN: USER_ROLES_CN.get(res.role) || '会员',
          description: group.description || '',
          monthStats: monthStats,
          memberList: memberList
        },
        monthStats: monthStats,
        memberList: memberList,
        // recentActivities 将由 attendRecords 替代，此处可移除或保留空数组
        recentActivities: []
      })
    } catch (error) {
      wx.showToast({
        title: '加载小组详情失败',
        icon: 'none'
      })
    } finally {
      this.setData({
        pageLoading: false,
      })
    }
  },

  // 邀请成员
  inviteMembers() {
    wx.showActionSheet({
      itemList: ['生成邀请链接', '生成二维码', '复制小组ID'],
      success: (res) => {
        if (res.tapIndex === 0) {
          this.generateInviteLink()
        } else if (res.tapIndex === 1) {
          this.generateQRCode()
        } else if (res.tapIndex === 2) {
          this.copyGroupId()
        }
      }
    })
  },

  // 生成邀请链接
  generateInviteLink() {
    const inviteLink = `小程序://打卡小程序/join-group?id=${this.data.groupId}`
    wx.setClipboardData({
      data: inviteLink,
      success: () => {
        wx.showToast({
          title: '邀请链接已复制',
          icon: 'success'
        })
      }
    })
  },

  // 生成二维码
  generateQRCode() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  // 复制小组ID
  copyGroupId() {
    wx.setClipboardData({
      data: this.data.groupId,
      success: () => {
        wx.showToast({
          title: '小组ID已复制',
          icon: 'success'
        })
      }
    })
  },

  // 管理小组
  manageGroup() {
    wx.navigateTo({
      url: `/pages/edit-group/edit-group?id=${this.data.groupId}`
    })
  },

  // movable-view 改变事件
  onMemberItemTap(e) {
    const index = e.currentTarget.dataset.index;
    const role = e.currentTarget.dataset.role;
    if (role === 'USER') return;
    const memberList = this.data.memberList;
    const member = memberList[index];
    const slideDistance = -member.buttonsWidth; // 按钮的总宽度

    // 如果当前项已经滑动，则收回；否则滑动
    member.x = member.x === slideDistance ? 0 : slideDistance;

    // 重置其他成员项的滑动状态
    memberList.forEach((m, i) => {
      if (i !== index) {
        m.x = 0;
      }
    });

    this.setData({
      memberList: memberList
    });
  },


  // 处理成员状态变更 (入组审核/移出小组)
  async handleMemberStatusChange(e) {
    const { index, role, status, action } = e.currentTarget.dataset;
    if (action === 'remove' && role === 'CREATOR') {
      return;
    }
    const { groupId, groupInfo } = this.data;
    const member = this.data.memberList[index];
    const memberName = member.name;

    let title = '';
    let content = '';
    let successMsg = '';
    let apiStatus = '';

    if (action === 'approve') {
      title = '确认入组';
      content = `是否确认【${memberName}】加入小组？`;
      successMsg = '入组审核成功';
      apiStatus = 'ACTIVE';
    } else if (action === 'remove') {
      title = '移出小组';
      content = `是否确认【${memberName}】移出小组？`;
      successMsg = '移出小组成功';
      apiStatus = 'INACTIVE';
    }

    wx.showModal({
      title: title,
      content: content,
      confirmText: action === 'approve' ? '确认' : '确认', // 确认和拒绝按钮
      cancelText: action === 'approve' ? '拒绝' : '取消',
      success: async (res) => {
        if (res.confirm) {
          try {
            await groupAPI.changeMemberStatus(groupId, member.id, apiStatus);
            wx.showToast({
              title: successMsg,
              icon: 'success'
            });
            this.loadGroupDetail(groupId); // 刷新页面
          } catch (error) {
            wx.showToast({
              title: `${title}失败`,
              icon: 'none'
            });
            console.error(`${title}失败`, error);
          }
        } else if (res.cancel && action === 'approve') { // 点击拒绝按钮
          try {
            await groupAPI.changeMemberStatus(groupId, member.id, 'INACTIVE');
            wx.showToast({
              title: '已拒绝入组',
              icon: 'success'
            });
            this.loadGroupDetail(groupId); // 刷新页面
          } catch (error) {
            wx.showToast({
              title: '拒绝入组失败',
              icon: 'none'
            });
            console.error('拒绝入组失败', error);
          }
        }
      }
    });
  },

  // 处理成员角色变更 (添加/移除管理权限)
  async handleMemberRoleChange(e) {
    const { index, role, action } = e.currentTarget.dataset;
    if (action === 'removeManager' && role === 'CREATOR') {
      return;
    }
    const { groupId } = this.data;
    const member = this.data.memberList[index];
    const memberName = member.name;

    let title = '';
    let content = '';
    let successMsg = '';
    let apiRole = '';

    if (action === 'addManager') {
      title = '添加管理权限';
      content = `是否确认【${memberName}】添加管理权限？`;
      successMsg = '添加管理权限成功';
      apiRole = 'MANAGER';
    } else if (action === 'removeManager') {
      title = '移除管理权限';
      content = `是否确认【${memberName}】移除管理权限？`;
      successMsg = '移除管理权限成功';
      apiRole = 'USER';
    }

    wx.showModal({
      title: title,
      content: content,
      success: async (res) => {
        if (res.confirm) {
          try {
            await groupAPI.changeMemberRole(groupId, member.id, apiRole);
            wx.showToast({
              title: successMsg,
              icon: 'success'
            });
            this.loadGroupDetail(groupId); // 刷新页面
          } catch (error) {
            wx.showToast({
              title: `${title}失败`,
              icon: 'none'
            });
            console.error(`${title}失败`, error);
          }
        }
      }
    });
  },

  // 查看全部成员
  showAllMembers() {
    // showAllMembers 也只用 this.data.memberList
  },

  // 处理解散或退出小组
  async handleDisbandOrLeaveGroup() {
    const { groupInfo, groupId } = this.data;
    const isCreator = groupInfo.role === 'CREATOR';
    const actionType = isCreator ? '解散' : '退出';
    const contentText = isCreator ? `是否确认解散【${groupInfo.name}】小组？` : `是否确认退出【${groupInfo.name}】小组？`;

    wx.showModal({
      title: `确认${actionType}`,
      content: contentText,
      success: async (res) => {
        if (res.confirm) {
          try {
            if (isCreator) {
              await groupAPI.disbandGroup(groupId);
            } else {
              await groupAPI.leaveGroup(groupId);
            }
            wx.showToast({
              title: `${actionType}成功`,
              icon: 'success'
            });
            setTimeout(() => {
              wx.navigateBack(); // 返回小组列表页
              // 刷新上一页（小组列表页）
              const pages = getCurrentPages();
              if (pages.length > 1) {
                const prevPage = pages[pages.length - 2];
                if (prevPage.route === 'miniprogram/pages/groups/groups') { // 假设小组列表页的路径是这个
                  prevPage.onLoad(); // 重新加载数据
                }
              }
            }, 1500);
          } catch (error) {
            wx.showToast({
              title: `${actionType}失败`,
              icon: 'none'
            });
            console.error(`${actionType}小组失败`, error);
          }
        }
      }
    });
  },

  // 退出小组 (原有的leaveGroup方法，现在由handleDisbandOrLeaveGroup统一处理，可以移除或保留作为备用)
  // leaveGroup() {
  //   this.handleDisbandOrLeaveGroup();
  // }

  // 显示打卡记录详情
  showCheckinDetail(e) {
    const record = e.currentTarget.dataset.record;
    this.setData({
      showCheckinDetailModal: true,
      currentCheckinDetail: record
    });
  },

  // 关闭打卡记录详情弹窗
  closeCheckinDetailModal() {
    this.setData({
      showCheckinDetailModal: false,
      currentCheckinDetail: null
    });
  },

  // 处理立即打卡
  handleCheckinNow() {
    const { groupId, attendedToday } = this.data;
    if (attendedToday) {
      return;
    }
    if (groupId) {
      wx.navigateTo({
        url: `/pages/checkin/checkin?groupId=${groupId}`
      });
    } else {
      wx.showToast({
        title: '小组ID获取失败',
        icon: 'none'
      });
    }
  },
})
