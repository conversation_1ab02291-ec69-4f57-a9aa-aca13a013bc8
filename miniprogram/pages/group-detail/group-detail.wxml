<!--group-detail.wxml-->
<loading show="{{pageLoading}}"></loading>
<view class="container">
  <!-- 小组头部信息 -->
  <view class="group-header">
    <view class="header-top">
      <view class="group-info">
        <view class="group-icon {{groupInfo.iconColor}}">
          <text>{{groupInfo.icon}}</text>
        </view>
        <view class="group-details">
          <text class="group-name">{{groupInfo.name}}</text>
          <text class="group-meta">{{groupInfo.memberCount}}名成员</text>
          <text class="group-invite-code" wx:if="{{groupInfo.role === 'CREATOR' || groupInfo.role === 'MANAGER'}}">
            邀请码 · {{groupInfo.inviteCode}}
          </text>
        </view>
      </view>
      <button
        class="action-btn-top"
        style="width:20%"
        bindtap="handleDisbandOrLeaveGroup"
        wx:if="{{groupInfo.role === 'CREATOR' || groupInfo.role === 'MANAGER' || groupInfo.role === 'USER'}}"
      >
        {{groupInfo.role === 'CREATOR' ? '解散' : '退出'}}
      </button>
    </view>
    <text class="group-description">{{groupInfo.description}}</text>
    <view class="group-actions">
<!--      <button class="action-btn invite" bindtap="inviteMembers" wx:if="{{groupInfo.role === 'CREATOR' || groupInfo.role === 'MANAGER'}}">-->
<!--        🔗 邀请成员-->
<!--      </button>-->
      <button class="action-btn manage" bindtap="manageGroup" wx:if="{{groupInfo.role === 'CREATOR' || groupInfo.role === 'MANAGER'}}">
        ⚙️ 管理设置
      </button>
      <button class="action-btn checkin" bindtap="handleCheckinNow">
        {{attendedTip}}
      </button>
    </view>
  </view>

  <!-- 本月统计 -->
  <view class="stats-card">
    <text class="card-title">本月统计</text>
    <view class="stats-grid">
      <view class="stat-item">
        <text class="stat-number">{{monthStats.targetDays}}</text>
        <text class="stat-label">目标天数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{monthStats.completedDays}}</text>
        <text class="stat-label">已完成</text>
      </view>
    </view>
    <view class="progress-section">
      <view class="progress-info">
        <text class="progress-label">整体完成率</text>
        <text class="progress-text">{{monthStats.completionRate}}%</text>
      </view>
      <view class="progress-bar">
        <view 
          class="progress-fill" 
          style="width: {{monthStats.completionRate}}%"
        ></view>
      </view>
    </view>
  </view>

  <!-- 成员列表 -->
  <view class="members-card">
    <view class="members-header">
      <text class="card-title">成员列表</text>
      <text class="member-count">{{groupInfo.memberCount}}人</text>
    </view>
    
    <view class="members-list">
      <movable-area class="member-movable-area" wx:for="{{memberList}}" wx:key="id">
        <movable-view
          class="member-item"
          direction="horizontal"
          damping="20"
          x="{{item.x}}"
          data-index="{{index}}"
          disabled="{{groupInfo.role === 'USER'}}"
        >
          <view class="member-content" bindtap="onMemberItemTap" data-index="{{index}}" data-role="{{groupInfo.role}}">
            <view class="member-info">
              <image class="member-avatar" src="{{ossPrefixUrl}}{{item.avatar}}" mode="aspectFill"></image>
              <view class="member-details">
                <view class="member-name-row">
                  <text class="member-name">{{item.name}}</text>
                  <view class="member-role {{item.role}}" wx:if="{{item.role === 'MANAGER' || item.role === 'CREATOR'}}">
                    <text>{{item.roleCN}}</text>
                  </view>
                  <view class="member-new" wx:if="{{item.isNew === 1}}">
                    <text>新成员</text>
                  </view>
                </view>
                <text class="member-progress">本月 {{item.currentCount}}/{{item.targetCount}} 次</text>
              </view>
            </view>
            <view class="member-status {{item.status}}">
              <text>{{item.statusIcon}}</text>
            </view>
          </view>
          <view class="member-action-buttons">
            <button
              wx:if="{{groupInfo.role === 'CREATOR' || groupInfo.role === 'MANAGER'}}"
              class="action-btn-swipe"
              bindtap="handleMemberStatusChange"
              data-index="{{index}}"
              data-role="{{item.role}}"
              data-status="{{item.status === 'PENDING' ? 'ACTIVE' : 'INACTIVE'}}"
              data-action="{{item.status === 'PENDING' ? 'approve' : 'remove'}}"
            >
              {{item.status === 'PENDING' ? '➕入组审核' : '❌移出小组'}}
            </button>
            <button
              wx:if="{{groupInfo.role === 'CREATOR'}}"
              class="action-btn-swipe"
              bindtap="handleMemberRoleChange"
              data-index="{{index}}"
              data-role="{{item.role}}"
              data-action="{{item.role === 'USER' ? 'addManager' : 'removeManager'}}"
            >
              {{item.role === 'USER' ? '➕管理权限' : '➖管理权限'}}
            </button>
          </view>
        </movable-view>
      </movable-area>
    </view>

    <button class="show-all-btn" bindtap="showAllMembers" wx:if="{{memberList.length < groupInfo.memberCount}}">
      查看全部成员
    </button>
  </view>

  <!-- 最近动态 -->
  <view class="activities-card">
    <text class="card-title">最近动态</text>
    <view class="activities-list">
      <view 
        class="activity-item"
        wx:for="{{attendRecords}}"
        wx:key="id"
        wx:for-item="record"
        bindtap="showCheckinDetail"
        data-record="{{record}}"
      >
        <image class="activity-avatar" src="{{ossPrefixUrl}}{{record.memberAvatar}}" mode="aspectFill"></image>
        <view class="activity-content">
          <view class="activity-header">
            <text class="activity-member-name">{{record.memberName}}</text>
            <text class="activity-time">{{record.attendanceTime}}</text>
          </view>
          <!-- 图片默认不展示，只在详情中展示 -->
          <text wx:if="{{record.displayComment}}" class="activity-comment">{{record.displayComment}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部加载提示 -->
  <view class="loading-more-footer" wx:if="{{isAttendRecordsLoading}}">
    <text>加载中...</text>
  </view>
  <view class="loading-more-footer" wx:if="{{attendRecords.length === 0 && !isAttendRecordsLoading}}">
    <text>暂无打卡记录</text>
  </view>

  <!-- 打卡详情弹窗 -->
  <view class="checkin-detail-modal-overlay" wx:if="{{showCheckinDetailModal}}" bindtap="closeCheckinDetailModal">
    <view class="checkin-detail-modal-content">
      <text class="modal-title">打卡详情</text>
      <scroll-view class="detail-scroll" scroll-y="true" style="flex: 1;">
        <view class="detail-item">
          <text class="detail-label">成员名称:</text>
          <text class="detail-value">{{currentCheckinDetail.memberName}}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">打卡时间:</text>
          <text class="detail-value">{{currentCheckinDetail.attendanceTime}}</text>
        </view>
        <view class="detail-item" wx:if="{{currentCheckinDetail.assetUrl}}">
          <text class="detail-label">打卡图片:</text>
          <image class="detail-image" src="{{ossPrefixUrl}}{{currentCheckinDetail.assetUrl}}" mode="aspectFit"></image>
        </view>
        <view class="detail-item" wx:if="{{currentCheckinDetail.comment}}">
          <text class="detail-label">打卡备注:</text>
          <text class="detail-value">{{currentCheckinDetail.comment}}</text>
        </view>
      </scroll-view>
      <button class="modal-close-btn" bindtap="closeCheckinDetailModal">关闭</button>
    </view>
  </view>
</view>
