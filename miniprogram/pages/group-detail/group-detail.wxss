/* group-detail.wxss */
.container {
  padding: 32rpx;
  background-color: #f6f6f6;
  min-height: 100vh;
}

/* 小组头部 */
.group-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 32rpx;
  padding: 48rpx;
  margin-bottom: 32rpx;
  color: white;
  box-shadow: 0 8rpx 40rpx rgba(102, 126, 234, 0.3);
}

.header-top {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32rpx;
  width: 120%;
}

.group-info {
  display: flex;
  align-items: center;
}

.group-icon {
  width: 128rpx;
  height: 128rpx;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 32rpx;
  font-size: 48rpx;
  background: rgba(255, 255, 255, 0.2);
}

.group-name {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.group-meta {
  display: block;
  font-size: 24rpx;
  opacity: 0.8;
}

.group-invite-code {
  position: absolute;
  font-size: 24rpx;
  opacity: 0.8;
  top: 167rpx;
}

.group-description {
  display: block;
  font-size: 28rpx;
  line-height: 1.6;
  opacity: 0.9;
  margin-bottom: 32rpx;
}

.group-actions {
  display: flex;
  gap: 24rpx;
}

.action-btn-top {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 24rpx;
  padding: 16rpx 24rpx;
  color: white;
  font-size: 28rpx;
  font-weight: 500;
  margin-left: auto; /* Pushes the button to the right */
}

.action-btn {
  flex: 1;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 24rpx;
  padding: 16rpx;
  color: white;
  font-size: 28rpx;
  font-weight: 500;
}

/* 卡片通用样式 */
.stats-card,
.members-card,
.activities-card {
  background: white;
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);
}

.card-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 32rpx;
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 32rpx;
  margin-bottom: 32rpx;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 8rpx;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  color: #666;
}

.progress-section {
  margin-bottom: 24rpx;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.progress-label {
  font-size: 28rpx;
  color: #666;
}

.progress-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.progress-bar {
  width: 100%;
  height: 16rpx;
  background: #f0f0f0;
  border-radius: 8rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4caf50 0%, #81c784 100%);
  border-radius: 8rpx;
  transition: width 0.3s ease;
}

/* 成员列表 */
.members-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.member-count {
  font-size: 28rpx;
  color: #666;
}

.members-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.member-movable-area {
  width: 100%;
  height: 120rpx; /* Adjust height to accommodate two buttons */
  position: relative;
  overflow: hidden;
  border-radius: 24rpx; /* Add border-radius for consistency */
  background-color: white; /* Ensure background for swipe */
}

.member-item {
  width: calc(100% + 362rpx); /* Original width + 2 buttons (180rpx each) */
  height: 100%;
  display: flex;
  align-items: center;
  background-color: white; /* Ensure background for swipe */
  box-sizing: border-box;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
  padding-right: 360rpx; /* Space for the buttons */
}

.member-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 90%; /* Occupy full width of the visible area */
  padding: 0 40rpx; /* Add padding to match card padding */
  flex-shrink: 0; /* Prevent content from shrinking */
}

.member-content-open {
  padding-right: 400rpx; /* Adjust padding to make space for buttons */
}

.member-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.member-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 24rpx;
}

.member-name-row {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.member-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-right: 16rpx;
}

.member-role {
  background: #fff3e0;
  color: #f57c00;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.member-new {
  background: #f8deb4;
  color: #6bbbf8;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.member-progress {
  font-size: 24rpx;
  color: #666;
}

.member-status {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.member-status.completed {
  background: #e8f5e8;
  color: #4caf50;
}

.member-status.pending {
  background: #fff3e0;
  color: #ff9800;
}

.member-status.warning {
  background: #ffebee;
  color: #f44336;
}

.member-action-buttons {
  display: flex;
  height: 100%;
  flex-shrink: 0;
  position: absolute;
  right: 0;
  top: 0;
  width: 360rpx; /* Width for two buttons */
}

.action-btn-swipe {
  height: 100%;
  width: 180rpx; /* Each button takes half the width */
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: white;
  border-radius: 0; /* Remove border-radius for buttons inside swipe */
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.action-btn-swipe:first-child {
  background-color: #4CAF50; /* Green for approve/remove */
}

.action-btn-swipe:last-child {
  background-color: #FFC107; /* Amber for manage permissions */
}

.action-btn-swipe::after {
  border: none;
}

.show-all-btn {
  width: 100%;
  background: #f5f5f5;
  color: #667eea;
  border: none;
  border-radius: 24rpx;
  padding: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  margin-top: 24rpx;
}

/* 最近动态 */
.activities-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  padding: 24rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.activity-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.activity-member-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.activity-time {
  font-size: 24rpx;
  color: #999;
}

.activity-image {
  width: 100%;
  height: 300rpx; /* 固定高度，可根据实际情况调整 */
  border-radius: 16rpx;
  margin-top: 16rpx;
  margin-bottom: 16rpx;
  object-fit: cover; /* 保持图片比例并裁剪 */
}

.activity-comment {
  font-size: 28rpx;
  color: #555;
  line-height: 1.6;
}

/* 退出小组按钮 */
.leave-btn {
  width: 100%;
  background: #ffebee;
  color: #f44336;
  border: 2rpx solid #ffcdd2;
  border-radius: 24rpx;
  padding: 32rpx;
  font-size: 32rpx;
  font-weight: 600;
}

.edit-icon {
  color: white;
  font-size: 24rpx;
  line-height: 1;
}

.edit-btn {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 64rpx;
  height: 64rpx;
  background: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  backdrop-filter: blur(10rpx);
  flex-shrink: 0;
}

/* 打卡详情弹窗样式 */
.checkin-detail-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-more-footer {
  text-align: center;
  padding: 20rpx 0;
  color: #999;
  font-size: 28rpx;
}

.checkin-detail-modal-content {
  background-color: #fff;
  border-radius: 24rpx;
  padding: 48rpx;
  width: 80%;
  max-height: 80%;
  display: flex;
  flex-direction: column;
  position: relative;
  /* 去掉overflow-y: auto，交给scroll-view处理 */
  /* overflow-y: auto; */
}

.detail-scroll {
  /* 让内容区撑满剩余空间并可滚动 */
  flex: 1;
  overflow-y: auto;
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 32rpx;
  text-align: center;
}

.detail-item {
  margin-bottom: 24rpx;
}

.detail-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
  display: block;
}

.detail-value {
  font-size: 32rpx;
  color: #333;
  word-break: break-all;
}

.detail-image {
  width: 100%;
  max-height: 400rpx;
  border-radius: 16rpx;
  margin-top: 16rpx;
  object-fit: contain; /* 确保图片完整显示 */
}

.modal-close-btn {
  margin-top: 24rpx;
  background-color: #667eea;
  color: #fff;
  border-radius: 24rpx;
  padding: 20rpx;
  font-size: 32rpx;
  text-align: center;
  /* 保持按钮大小 */
  flex-shrink: 0;
}