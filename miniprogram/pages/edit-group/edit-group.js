// edit-group.js
const { authManager, USER_ROLES } = require('../../utils/auth.js')
const { groupAPI } = require('../../utils/api.js')
const config = require("../../utils/config");

const iconOptions = [
  { icon: '🏃', color: 'blue' },
  { icon: '📚', color: 'purple' },
  { icon: '💪', color: 'green' },
  { icon: '🎵', color: 'yellow' },
  { icon: '🎨', color: 'red' },
  { icon: '💻', color: 'indigo' },
  { icon: '❤️', color: 'pink' },
  { icon: '🌱', color: 'teal' },
  { icon: '📷', color: 'orange' },
  { icon: '➕', color: 'gray' }
]

Page({
  data: {
    iconOptions,
    groupId: '',
    selectedIcon: '🏃',
    selectedIconColor: 'blue',
    groupName: '',
    groupDescription: '',
    targetCount: 22,
    enableReminder: true,
    doPublic: true,
    updating: false,
    canUpdate: false,
    originalData: null
  },

  onLoad(options) {
    if (options.id) {
      this.setData({
        groupId: options.id
      })
      this.loadGroupInfo(options.id)
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  /**
   * 加载小组信息
   */
  async loadGroupInfo(groupId) {
    try {
      const res = await groupAPI.getGroupBriefDetail(groupId)
      this.setData({
        selectedIcon: res.metadata.icon,
        selectedIconColor: res.metadata.iconColor,
        groupName: res.name,
        groupDescription: res.description,
        doPublic: res.doPublic,
        enableReminder: res.metadata.enableReminder,
        targetCount: res.metadata.targetCount,
        originalData: res,
        canUpdate: true
      })
    } catch (error) {
      console.error('加载小组信息失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    } finally {
      wx.hideLoading()
    }
  },

  // 选择图标
  selectIcon(e) {
    const { icon, color } = e.currentTarget.dataset
    this.setData({
      selectedIcon: icon,
      selectedIconColor: color
    })
    this.checkCanUpdate()
  },

  // 小组名称输入
  onGroupNameInput(e) {
    this.setData({
      groupName: e.detail.value
    }, () => {
      this.checkCanUpdate()
    })
  },

  // 小组描述输入
  onGroupDescriptionInput(e) {
    this.setData({
      groupDescription: e.detail.value
    })
    this.checkCanUpdate()
  },

  // 减少目标次数
  decreaseTarget() {
    if (this.data.targetCount > 1) {
      this.setData({
        targetCount: this.data.targetCount - 1
      })
    }
  },

  // 增加目标次数
  increaseTarget() {
    if (this.data.targetCount < 31) {
      this.setData({
        targetCount: this.data.targetCount + 1
      })
    }
  },

  // 手动输入目标次数
  onTargetCountInput(e) {
    const value = parseInt(e.detail.value) || 1
    // 实时更新，但不做范围限制（在blur时处理）
    this.setData({
      targetCount: value
    })
  },

  // 输入框失去焦点时验证范围
  onTargetCountBlur(e) {
    let value = parseInt(e.detail.value) || 1

    // 限制范围在1-31之间
    if (value < 1) {
      value = 1
    } else if (value > 31) {
      value = 31
    }

    // 如果值发生了变化，更新显示
    if (value !== this.data.targetCount) {
      this.setData({
        targetCount: value
      })

      // 提示用户范围限制
      if (parseInt(e.detail.value) > 31) {
        wx.showToast({
          title: '最大值为31次',
          icon: 'none',
          duration: 1500
        })
      } else if (parseInt(e.detail.value) < 1) {
        wx.showToast({
          title: '最小值为1次',
          icon: 'none',
          duration: 1500
        })
      }
    }
  },

  // 提醒设置变化
  onReminderChange(e) {
    wx.nextTick(() => {
      this.setData({
        enableReminder: e.detail.value
      })
      this.checkCanUpdate()
    })
  },

  // 设置隐私
  setPrivacy(e) {
    const doPublic = e.currentTarget.dataset.public
    this.setData({
      doPublic
    })
    this.checkCanUpdate()
  },

  // 检查是否可以更新
  checkCanUpdate() {
    const { groupName, originalData } = this.data

    if (!originalData) {
      this.setData({ canUpdate: false })
      return
    }

    // 检查是否有变化
    const hasChanges = (
      groupName.trim() !== originalData.name ||
      this.data.groupDescription !== originalData.description ||
      this.data.selectedIcon !== originalData.metadata.icon ||
      this.data.selectedIconColor !== originalData.metadata.iconColor ||
      this.data.enableReminder !== originalData.metadata.enableReminder ||
      this.data.doPublic !== originalData.metadata.doPublic
    )

    const canUpdate = groupName.trim().length > 0 && hasChanges

    this.setData({
      canUpdate
    })
  },

  // 更新小组
  async updateGroup() {
    if (!this.data.canUpdate || this.data.updating) {
      return
    }

    if (this.data.groupName.trim().length === 0) {
      wx.showToast({
        title: '请输入小组名称',
        icon: 'none'
      })
      return
    }

    this.setData({
      updating: true
    })

    try {
      // 构造新接口所需参数
      const updateData = {
        id: parseInt(this.data.groupId),
        name: this.data.groupName.trim(),
        description: this.data.groupDescription,
        doPublic: this.data.doPublic,
        metadata: {
          icon: this.data.selectedIcon,
          iconColor: this.data.selectedIconColor,
          enableReminder: this.data.enableReminder,
          targetCount: this.data.targetCount,
        },
      }
      await groupAPI.updateGroup(this.data.groupId, updateData)

      wx.showToast({
        title: '更新成功！',
        icon: 'success'
      })

      const gId = this.data.groupId;
      var pages = getCurrentPages();
      var prevPage = pages[pages.length - 2];
      var delta = pages.length - prevPage.index - 1;
      wx.navigateBack({
        delta: delta,
        success: function () {
          prevPage.onLoad({id: gId}); // 执行上一页的onLoad函数
        }
      });

    } catch (error) {
      console.error('更新小组失败:', error)
      let errorMessage = '更新失败，请重试'
      if (error.code === 'CLUB1401') {
        errorMessage = '小组名称已存在，请换一个'
      } else if (error.code === 'CLUB1601') {
        errorMessage = '图片内容违规！'
      } else if (error.code === 'CLUB1602') {
        errorMessage = '文字内容违规！'
      }
      wx.showToast({
        title: errorMessage,
        icon: 'none'
      })
    } finally {
      this.setData({
        updating: false
      })
    }
  }
})
