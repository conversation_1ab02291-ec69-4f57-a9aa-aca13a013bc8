// join-group.js
const { authManager, USER_ROLES } = require('../../utils/auth.js')
const { groupAPI } = require('../../utils/api.js')
const config = require("../../utils/config");

Page({
  data: {
    currentMethod: 'code',
    inviteCode: '',
    foundGroup: null,
    searchAttempted: false,
    joining: false,
    recommendedGroups: [],
    ossPrefixUrl: config.ossPrefixUrl,
    joined: false,
  },

  onLoad() {
    this.checkPermission()
  },

  /**
   * 检查加入小组权限
   */
  async checkPermission() {
    try {
      authManager.refreshUser()

      if (!authManager.canJoinGroup()) {
        wx.showModal({
          title: '权限不足',
          content: '您当前没有加入小组的权限，请先完善个人信息',
          showCancel: false,
          success: () => {
            wx.navigateBack()
          }
        })
        return
      }
    } catch (error) {
      console.error('权限检查失败:', error)
      wx.showToast({
        title: '权限检查失败',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  onReady() {
    this.loadRecommendedGroups()
  },

  // 切换加入方式
  switchMethod(e) {
    const method = e.currentTarget.dataset.method
    this.setData({
      currentMethod: method,
      foundGroup: null,
      searchAttempted: false
    })
  },

  // 邀请码输入
  onCodeInput(e) {
    this.setData({
      inviteCode: e.detail.value.toUpperCase()
    })
  },

  // 搜索小组
  async searchGroup() {
    if (this.data.inviteCode.length !== 6) {
      return
    }
    try {
      const res = await groupAPI.searchGroup(this.data.inviteCode)
      const userId = wx.getStorageSync(config.storageKeys.userInfo).userId
      // 适配数据结构
      const foundGroup = res ? {
        ...res,
        icon: res.metadata.icon,
        targetCount: res.metadata.targetCount,
        completionRate: res.completeRate,
        recentMembers: res.groupMembers.map(m => {
          if (!this.data.joined && m.user.id === userId) {
            this.setData({
              joined: true
            })
          }
          return ({
            id: m.user.id,
            avatar: m.user.wxUser.avatar
          })
        })
      } : null
      this.setData({
        foundGroup,
        searchAttempted: true
      })
      if (!foundGroup) {
        wx.showToast({
          title: '未找到小组',
          icon: 'none'
        })
      }
    } catch (error) {
      this.setData({
        foundGroup: null,
        searchAttempted: true
      })
      wx.showToast({
        title: '查找小组失败',
        icon: 'none'
      })
    }
  },

  // 扫描二维码
  scanQRCode() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
    //
    // wx.scanCode({
    //   success: (res) => {
    //     // 解析二维码内容
    //     const scannedData = res.result
    //
    //     // 这里应该解析二维码中的小组信息
    //     // 目前模拟扫描到邀请码
    //     if (scannedData.includes('ABC123')) {
    //       this.setData({
    //         inviteCode: 'ABC123'
    //       })
    //       this.searchGroup()
    //     } else {
    //       wx.showToast({
    //         title: '无效的二维码',
    //         icon: 'none'
    //       })
    //     }
    //   },
    //   fail: (err) => {
    //     console.error('扫码失败:', err)
    //     wx.showToast({
    //       title: '扫码失败',
    //       icon: 'none'
    //     })
    //   }
    // })
  },

  // 加载推荐小组
  async loadRecommendedGroups() {
    try {
      const res = await groupAPI.getRecommendedGroups()
      // 适配数据结构
      const recommendedGroups = (res || []).map(g => {
        const groupInfo = ({
          id: g.id,
          name: g.name,
          icon: g.metadata && g.metadata.icon || '📝',
          iconColor: g.metadata && g.metadata.iconColor || 'blue',
          memberCount: g.memberCount || 0
        });

        const userId = wx.getStorageSync(config.storageKeys.userInfo).userId;
        if (g.groupMembers && g.groupMembers[0].user && g.groupMembers[0].user.id === userId) {
          return {
            ...groupInfo,
            status: g.groupMembers[0].status,
          }
        }

        return groupInfo
      })
      this.setData({
        recommendedGroups
      })
    } catch (error) {
      wx.showToast({
        title: '加载推荐小组失败',
        icon: 'none'
      })
    }
  },

  // 预览小组
  async previewGroup(e) {
    const group = e.currentTarget.dataset.group
    if (group.status && (group.status === 'ACTIVE' || group.status === 'PENDING')) {
      return
    }
    const res = await groupAPI.getGroupBriefDetail(group.id)
    const userId = wx.getStorageSync(config.storageKeys.userInfo).userId
    this.setData({
      foundGroup: {
        ...res,
        icon: res.metadata.icon,
        targetCount: res.metadata.targetCount,
        completionRate: res.completeRate,
        recentMembers: res.groupMembers.map(m => {
          if (!this.data.joined && m.user.id === userId) {
            this.setData({
              joined: true
            })
          }
          return ({
            id: m.user.id,
            avatar: m.user.wxUser.avatar
          })
        })
      }
    })
  },

  // 加入小组
  joinGroup(e) {
    this.performJoin(this.data.foundGroup.id)
  },

  // 执行加入操作
  async performJoin(groupId) {
    this.setData({
      joining: true
    })

    try {
      // 调用加入小组接口
      await groupAPI.joinGroup(groupId, this.data.inviteCode)

      wx.showToast({
        title: '加入成功！',
        icon: 'success'
      })

      // 延迟跳转到小组详情
      setTimeout(() => {
        wx.switchTab(
          {
            url: '/pages/groups/groups'
          }
        )
      }, 500)

    } catch (error) {
      console.error('加入小组失败:', error)

      let errorMessage = '加入失败，请重试'
      if (error.code === 'CLUB1403') {
        errorMessage = '您已经是该小组成员'
      }
      if (error.code === 'CLUB1406') {
        errorMessage = '邀请码错误'
      }

      wx.showToast({
        title: errorMessage,
        icon: 'none'
      })
    } finally {
      this.setData({
        joining: false
      })
    }
  }
})
