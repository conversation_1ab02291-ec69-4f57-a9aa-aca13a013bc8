// notifications.js

// Mock通知数据
const mockNotifications = [
  {
    id: 1,
    type: 'checkin',
    icon: '🔔',
    title: '打卡提醒',
    message: '今天还没有打卡哦，记得完成今日目标！',
    timestamp: Date.now() - 5 * 60 * 1000, // 5分钟前
    read: false
  },
  {
    id: 2,
    type: 'group',
    icon: '👥',
    title: '小组消息',
    message: '@张小美 恭喜你在"晨跑俱乐部"中排名第一！',
    timestamp: Date.now() - 10 * 60 * 1000, // 10分钟前
    read: false
  },
  {
    id: 3,
    type: 'reminder',
    icon: '⚠️',
    title: '月度提醒',
    message: '本月还需打卡4次才能达到目标，加油！',
    timestamp: Date.now() - 60 * 60 * 1000, // 1小时前
    read: false
  },
  {
    id: 4,
    type: 'achievement',
    icon: '🏆',
    title: '成就解锁',
    message: '恭喜解锁"连续7天"成就徽章！',
    timestamp: Date.now() - 24 * 60 * 60 * 1000, // 昨天
    read: true
  },
  {
    id: 5,
    type: 'group',
    icon: '👤',
    title: '新成员加入',
    message: '李小明加入了"晨跑俱乐部"',
    timestamp: Date.now() - 24 * 60 * 60 * 1000 - 6 * 60 * 60 * 1000, // 昨天18:45
    read: true
  },
  {
    id: 6,
    type: 'system',
    icon: '⚙️',
    title: '系统更新',
    message: '小程序已更新至v2.1.0，新增统计功能',
    timestamp: Date.now() - 2 * 24 * 60 * 60 * 1000, // 2天前
    read: true
  },
  {
    id: 7,
    type: 'social',
    icon: '❤️',
    title: '点赞通知',
    message: '王小红为你的打卡记录点赞了',
    timestamp: Date.now() - 3 * 24 * 60 * 60 * 1000, // 3天前
    read: true
  },
  {
    id: 8,
    type: 'checkin',
    icon: '✅',
    title: '打卡成功',
    message: '6月25日打卡成功，继续保持！',
    timestamp: Date.now() - 3 * 24 * 60 * 60 * 1000, // 3天前
    read: true
  }
]

Page({
  data: {
    currentCategory: 'all',
    notifications: mockNotifications,
    filteredNotifications: mockNotifications,
    hasMore: false,
    categoryNames: {
      all: '',
      checkin: '打卡提醒',
      group: '小组消息',
      system: '系统通知'
    }
  },

  onLoad() {
    this.loadNotifications()
    this.filterNotifications()
  },

  // 加载通知数据
  loadNotifications() {
    // 从本地存储获取通知数据
    const localNotifications = wx.getStorageSync('notifications') || []
    
    // 合并本地数据和mock数据
    const allNotifications = [...localNotifications, ...mockNotifications]
    
    // 按时间排序
    allNotifications.sort((a, b) => b.timestamp - a.timestamp)
    
    // 添加时间文本
    const notificationsWithTime = allNotifications.map(notification => ({
      ...notification,
      timeText: this.formatTime(notification.timestamp)
    }))

    this.setData({
      notifications: notificationsWithTime
    })
  },

  // 格式化时间
  formatTime(timestamp) {
    const now = Date.now()
    const diff = now - timestamp
    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))

    if (minutes < 1) {
      return '刚刚'
    } else if (minutes < 60) {
      return `${minutes}分钟前`
    } else if (hours < 24) {
      return `${hours}小时前`
    } else if (days === 1) {
      return '昨天'
    } else if (days < 7) {
      return `${days}天前`
    } else {
      const date = new Date(timestamp)
      return `${date.getMonth() + 1}月${date.getDate()}日`
    }
  },

  // 切换分类
  switchCategory(e) {
    const category = e.currentTarget.dataset.category
    this.setData({
      currentCategory: category
    })
    this.filterNotifications()
  },

  // 过滤通知
  filterNotifications() {
    const { currentCategory, notifications } = this.data
    let filtered = notifications

    if (currentCategory !== 'all') {
      filtered = notifications.filter((notification) => 
        notification.type === currentCategory
      )
    }

    this.setData({
      filteredNotifications: filtered
    })
  },

  // 标记为已读
  markAsRead(e) {
    const notificationId = e.currentTarget.dataset.id
    const { notifications } = this.data
    
    const updatedNotifications = notifications.map((notification) => {
      if (notification.id === notificationId) {
        return { ...notification, read: true }
      }
      return notification
    })

    this.setData({
      notifications: updatedNotifications
    })
    
    this.filterNotifications()
    this.saveNotifications(updatedNotifications)
  },

  // 全部已读
  markAllRead() {
    const { notifications } = this.data
    const updatedNotifications = notifications.map((notification) => ({
      ...notification,
      read: true
    }))

    this.setData({
      notifications: updatedNotifications
    })
    
    this.filterNotifications()
    this.saveNotifications(updatedNotifications)
    
    wx.showToast({
      title: '已全部标记为已读',
      icon: 'success'
    })
  },

  // 清空通知
  clearAll() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有通知吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            notifications: [],
            filteredNotifications: []
          })
          
          this.saveNotifications([])
          
          wx.showToast({
            title: '已清空所有通知',
            icon: 'success'
          })
        }
      }
    })
  },

  // 加载更多
  loadMore() {
    wx.showToast({
      title: '没有更多消息了',
      icon: 'none'
    })
  },

  // 保存通知到本地存储
  saveNotifications(notifications) {
    // 只保存用户生成的通知，不保存mock数据
    const userNotifications = notifications.filter((notification) => 
      !mockNotifications.some(mock => mock.id === notification.id)
    )
    wx.setStorageSync('notifications', userNotifications)
  },

  // 页面显示时刷新数据
  onShow() {
    this.loadNotifications()
    this.filterNotifications()
  }
})
