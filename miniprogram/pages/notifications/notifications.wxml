<!--notifications.wxml-->
<view class="container">
  <!-- 通知分类 -->
  <view class="category-tabs">
    <button 
      class="tab-btn {{currentCategory === 'all' ? 'active' : 'inactive'}}" 
      bindtap="switchCategory"
      data-category="all"
    >
      全部
    </button>
    <button 
      class="tab-btn {{currentCategory === 'checkin' ? 'active' : 'inactive'}}" 
      bindtap="switchCategory"
      data-category="checkin"
    >
      打卡提醒
    </button>
    <button 
      class="tab-btn {{currentCategory === 'group' ? 'active' : 'inactive'}}" 
      bindtap="switchCategory"
      data-category="group"
    >
      小组消息
    </button>
    <button 
      class="tab-btn {{currentCategory === 'system' ? 'active' : 'inactive'}}" 
      bindtap="switchCategory"
      data-category="system"
    >
      系统通知
    </button>
  </view>

  <!-- 操作栏 -->
  <view class="action-bar" wx:if="{{filteredNotifications.length > 0}}">
    <button class="action-btn" bindtap="markAllRead">全部已读</button>
    <button class="action-btn" bindtap="clearAll">清空通知</button>
  </view>

  <!-- 通知列表 -->
  <view class="notifications-list">
    <view 
      class="notification-item {{item.read ? 'read' : 'unread'}}" 
      wx:for="{{filteredNotifications}}" 
      wx:key="id"
      bindtap="markAsRead"
      data-id="{{item.id}}"
    >
      <view class="notification-content">
        <view class="notification-header">
          <view class="notification-icon {{item.type}}">
            <text>{{item.icon}}</text>
          </view>
          <view class="notification-info">
            <text class="notification-title">{{item.title}}</text>
            <text class="notification-message">{{item.message}}</text>
          </view>
          <view class="unread-indicator" wx:if="{{!item.read}}"></view>
        </view>
        <text class="notification-time">{{item.timeText}}</text>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{filteredNotifications.length === 0}}">
    <view class="empty-icon">🔔</view>
    <text class="empty-title">暂无{{categoryNames[currentCategory]}}通知</text>
    <text class="empty-subtitle">有新消息时会在这里显示</text>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{filteredNotifications.length > 0 && hasMore}}">
    <button class="load-more-btn" bindtap="loadMore">加载更多消息</button>
  </view>
</view>
