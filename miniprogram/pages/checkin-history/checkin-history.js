// checkin-history.js
const config = require('../../utils/config.js')
const { userAPI } = require("../../utils/api");

Page({
  data: {
    currentYear: 0,
    currentMonth: 0,
    currentMonthText: '',
    canGoNext: false,
    weekDays: ['日', '一', '二', '三', '四', '五', '六'],
    calendarDays: [],
    monthStats: {
      checkedDays: 0,
      missedDays: 0,
      completionRate: 0
    },
    selectedDate: null,
    selectedDateDetail: null,
    ossPrefixUrl: config.ossPrefixUrl,
    pageLoading: true,
  },

  onLoad() {
    this.initCalendar()
  },

  // 初始化日历
  initCalendar() {
    const now = new Date()
    this.setData({
      currentYear: now.getFullYear(),
      currentMonth: now.getMonth() + 1,
      pageLoading: true,
    })
    this.generateCalendar()
  },

  getNowMonth() {
    const date = new Date();
    return (date.getMonth() + 1).toString().padStart(2, '0');
  },

  getNowDay() {
    const date = new Date();
    return date.getDate().toString().padStart(2, '0');
  },

  // 生成日历
  async generateCalendar() {
    try {
      const attendanceRecords = await userAPI.getAttendanceRecords()
      // 使用reduce进行分组
      const recordsMap = attendanceRecords.reduce((acc, record) => {
        const key = record.attendanceDate;
        if (!acc[key]) {
          acc[key] = [];
        }
        acc[key].push(record);
        return acc;
      }, {});

      const { currentYear, currentMonth } = this.data
      const now = new Date()
      const firstDay = new Date(currentYear, currentMonth - 1, 1)
      const startDate = new Date(firstDay)
      startDate.setDate(startDate.getDate() - firstDay.getDay())

      const calendarDays = []

      const date = new Date();

      // 生成6周的日期
      for (let week = 0; week < 6; week++) {
        for (let day = 0; day < 7; day++) {
          const currentDate = new Date(startDate)
          currentDate.setDate(startDate.getDate() + week * 7 + day)

          const dateKey = this.formatDateKey(currentDate)
          const checkCount = recordsMap[dateKey] ? recordsMap[dateKey].length : 0;

          const isCurrentYear = currentDate.getFullYear() === date.getFullYear()
          const isCurrentMonth = currentDate.getMonth() === date.getMonth()
          const isCurrentDay = currentDate.getDate() === date.getDate()
          const isToday = isCurrentYear && isCurrentMonth && isCurrentDay

          let type = 'current-month'
          if (!isCurrentMonth) {
            type = 'other-month'
          } else if (isToday) {
            type = 'today'
          } else if (checkCount > 0) {
            type = 'checked'
          }

          calendarDays.push({
            date: dateKey,
            day: currentDate.getDate(),
            type,
            hasCheckin: checkCount > 0,
            checkCount: checkCount,
            status: checkCount > 0 ? 'completed' : '',
            statusIcon: checkCount > 0 ? '✓' : ''
          })
        }
      }
      // 计算月度统计
      const monthStats = this.calculateMonthStats(recordsMap, currentYear, currentMonth)

      // 检查是否可以前进到下个月
      const canGoNext = currentMonth < now.getMonth() + 1 || currentYear < now.getFullYear()

      let recordsList = (Array.isArray(attendanceRecords) ? attendanceRecords.slice(0, 3) : [])
        .filter(Boolean)
        .map(item => {
          if (item) {
            return {
              imageUrl: item.assetUrl,
              groupName: item.group.name,
              time: item.attendanceTime.replace(/-/g, "/"),
              comment: item.comment,
              displayComment: item.comment && item.comment.length > 10 ? item.comment.substring(0, 10) + '...' : item.comment
            }
          }
        })
      this.setData({
        calendarDays,
        monthStats,
        canGoNext,
        currentMonthText: `${currentYear}年${currentMonth}月`,
        recordsMap,
        recordsList
      })
    } catch (error) {
      wx.hideLoading()
      console.error('打卡日历加载失败:', error)
      wx.showToast({
        title: '打卡日历加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({
        pageLoading: false,
      })
    }
  },

  // 计算月度统计
  calculateMonthStats(checkinData, year, month) {
    const daysInMonth = new Date(year, month, 0).getDate()
    const today = new Date()
    const isCurrentMonth = year === today.getFullYear() && month === today.getMonth()
    const currentDay = isCurrentMonth ? today.getDate() : daysInMonth

    let checkedDays = 0
    for (let day = 1; day <= currentDay; day++) {
      const dateKey = `${year}-${month.toString().padStart(2, '0')}-${day}`
      if (checkinData[dateKey]) {
        checkedDays++
      }
    }

    const missedDays = daysInMonth - checkedDays
    const completionRate = checkedDays > 0 ? Math.round((checkedDays / daysInMonth) * 100) : 0

    return {
      checkedDays,
      missedDays,
      completionRate
    }
  },

  // 格式化日期键
  formatDateKey(date) {
    const year = date.getFullYear();
    // 月份需要+1，因为getMonth()返回0-11
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  // 判断是否是同一天
  isSameDate(date1, date2) {
    return date1.getFullYear() === date2.getFullYear() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getDate() === date2.getDate()
  },

  // 上一个月
  prevMonth() {
    let { currentYear, currentMonth } = this.data
    currentMonth--
    if (currentMonth < 1) {
      currentMonth = 12
      currentYear--
    }
    this.setData({
      currentYear,
      currentMonth,
      selectedDate: false,
      selectedDateDetail: null
    })
    this.generateCalendar()
  },

  // 下一个月
  nextMonth() {
    if (!this.data.canGoNext) return

    let { currentYear, currentMonth } = this.data
    currentMonth++
    if (currentMonth > 12) {
      currentMonth = 1
      currentYear++
    }
    this.setData({
      currentYear,
      currentMonth,
      selectedDate: false,
      selectedDateDetail: null
    })
    this.generateCalendar()
  },

  // 选择日期
  selectDate(e) {
    const { date, hasCheckin } = e.currentTarget.dataset
    if (hasCheckin) {
      this.showDateDetail(date)
    } else {
      this.setData({
        selectedDateDetail: {
          dateText: this.formatDateText(date),
          hasCheckin: false,
          selectOne: false,
          status: 'missed',
          statusText: '未打卡'
        },
        selectedDate: date
      })
    }
  },

  // 显示日期详情
  showDateDetail(dateKey) {
    let { recordsMap } = this.data
    const records = recordsMap[dateKey];
    const selectedDateCheckinHistory = [];
    records.map(record => {
      selectedDateCheckinHistory.push({
        imageUrl: record.assetUrl,
        groupName: record.group.name,
        time: new Date(record.attendanceTime).toISOString().slice(0, 19).replace('T', ' ').replace(/-/g, "/"),
        comment: record.comment,
        displayComment: record.comment && record.comment.length > 10 ? record.comment.substring(0, 10) + '...' : record.comment
      })
    })
    if (selectedDateCheckinHistory) {
      this.setData({
        selectedDate: dateKey,
        selectedDateCheckinHistory,
        selectedDateDetail: {
          dateText: this.formatDateText(dateKey),
          hasCheckin: true,
          selectOne: false,
        }
      })
    } else {
      this.setData({
        selectedDate: dateKey
      })
    }
  },

  // 格式化日期文本
  formatDateText(dateKey) {
    const [year, month, day] = dateKey.split('-')
    return `${year}年${month}月${day}日`
  },

  // 格式化时间
  formatTime(timestamp) {
    const date = new Date(timestamp)
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    return `${hours}:${minutes}`
  },

  // 查看历史详情
  viewHistoryDetail(e) {
    const item = e.currentTarget.dataset.item
    this.setData({
      showCheckinDetailModal: true,
      selectedDateDetail: {
        hasCheckin: true,
        selectOne: true,
        imageUrl: item.imageUrl,
        comment: item.comment,
        time: item.time,
        groupName: item.groupName,
      }
    })
  },

  // 关闭打卡记录详情弹窗
  closeCheckinDetailModal() {
    this.setData({
      showCheckinDetailModal: false,
      selectedDateDetail: {
        hasCheckin: true,
        selectOne: false,
      }
    });
  },

  // 去打卡
  goToCheckin() {
    wx.navigateTo({
      url: '/pages/checkin/checkin'
    })
  }
})
