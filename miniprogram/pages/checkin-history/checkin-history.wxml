<!--checkin-history.wxml-->
<loading show="{{pageLoading}}"></loading>
<view class="container">
  <view class="calendar-card">
    <!-- 月份导航，移到卡片顶部 -->
    <view class="month-nav">
      <button class="nav-btn" bindtap="prevMonth">
        <text class="nav-icon">‹</text>
      </button>
      <text class="month-title">{{currentMonth}}</text>
      <button class="nav-btn" bindtap="nextMonth" disabled="{{!canGoNext}}">
        <text class="nav-icon">›</text>
      </button>
    </view>
    <!-- 星期标题 -->
    <view class="week-header">
      <text class="week-day" wx:for="{{weekDays}}" wx:key="*this">{{item}}</text>
    </view>
    
    <!-- 日期网格 -->
    <view class="calendar-grid">
      <view 
        class="calendar-day {{item.type}} {{selectedDate === item.date ? 'selected' : ''}}" 
        wx:for="{{calendarDays}}" 
        wx:key="date"
        bindtap="selectDate"
        data-date="{{item.date}}"
        data-has-checkin="{{item.hasCheckin}}"
      >
        <text class="day-number">{{item.day}}</text>
<!--        <view class="day-indicator {{item.status}}" wx:if="{{item.hasCheckin}}">-->
<!--          <text class="indicator-icon">{{item.statusIcon}}</text>-->
<!--        </view>-->
        <!-- 新增蓝色三角区域 -->
        <view wx:if="{{item.hasCheckin}}" class="checkin-corner">
          <text class="checkin-count">{{item.checkCount}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 本月统计 -->
  <view class="stats-card">
    <text class="card-title">本月统计</text>
    <view class="stats-grid">
      <view class="stat-item">
        <text class="stat-number checked">{{monthStats.checkedDays}}</text>
        <text class="stat-label">已打卡天数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number missed">{{monthStats.missedDays}}</text>
        <text class="stat-label">未打卡天数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number rate">{{monthStats.completionRate}}%</text>
        <text class="stat-label">打卡完成率</text>
      </view>
    </view>
  </view>

  <!-- 选中日期的打卡详情 -->
  <view class="detail-card" wx:if="{{selectedDate}}">
    <view class="detail-header">
      <text class="detail-title">{{selectedDate}} 打卡详情</text>
    </view>

    <view class="history-list" wx:if="{{!selectedDateDetail.selectOne && selectedDateDetail.hasCheckin}}">
      <view
              class="history-item"
              wx:for="{{selectedDateCheckinHistory}}"
              wx:key="id"
              bindtap="viewHistoryDetail"
              data-item="{{item}}"
      >
        <image src="{{ossPrefixUrl}}{{item.imageUrl}}" mode="aspectFill" class="history-image"></image>
        <view class="history-info">
          <text class="history-date">{{item.time}}</text>
          <text class="history-note">{{item.displayComment || ''}}</text>
          <text class="history-group">{{item.groupName}}</text>
        </view>
        <view class="history-status {{item.status}}">
          <text>{{item.statusIcon}}</text>
        </view>
      </view>
    </view>

    <view class="no-checkin" wx:if="{{!selectedDateDetail.selectOne && !selectedDateDetail.hasCheckin}}">
      <view class="no-checkin-icon">📅</view>
      <text class="no-checkin-text">这一天没有打卡记录</text>
    </view>
  </view>

  <!-- 打卡历史列表 -->
  <view class="history-card" wx:if="{{!selectedDate && recordsList.length > 0}}">
    <text class="card-title">最近打卡</text>
    <view class="history-list">
      <view 
        class="history-item" 
        wx:for="{{recordsList}}"
        wx:key="id"
        bindtap="viewHistoryDetail"
        data-item="{{item}}"
      >
        <image src="{{ossPrefixUrl}}{{item.imageUrl}}" mode="aspectFill" class="history-image"></image>
        <view class="history-info">
          <text class="history-date">{{item.time}}</text>
          <text class="history-note">{{item.displayComment || ''}}</text>
          <text class="history-group">{{item.groupName}}</text>
        </view>
        <view class="history-status {{item.status}}">
          <text>{{item.statusIcon}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{checkinHistory.length === 0}}">
    <view class="empty-icon">📝</view>
    <text class="empty-title">还没有打卡记录</text>
    <text class="empty-subtitle">开始你的第一次打卡吧</text>
    <button class="empty-action" bindtap="goToCheckin">立即打卡</button>
  </view>

  <!-- 打卡详情弹窗 -->
  <view class="checkin-detail-modal-overlay" wx:if="{{selectedDateDetail.selectOne && selectedDateDetail.hasCheckin}}" bindtap="closeCheckinDetailModal">
    <view class="checkin-detail-modal-content">
      <text class="modal-title">打卡详情</text>
      <scroll-view class="detail-scroll" scroll-y="true" style="flex: 1;">
        <view class="detail-item">
          <text class="detail-label">打卡小组:</text>
          <text class="detail-value">{{selectedDateDetail.groupName}}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">打卡时间:</text>
          <text class="detail-value">{{selectedDateDetail.time}}</text>
        </view>
        <view class="detail-item" wx:if="{{selectedDateDetail.imageUrl}}">
          <text class="detail-label">打卡图片:</text>
          <image class="detail-image" src="{{ossPrefixUrl}}{{selectedDateDetail.imageUrl}}" mode="aspectFit"></image>
        </view>
        <view class="detail-item" wx:if="{{selectedDateDetail.comment}}">
          <text class="detail-label">打卡备注:</text>
          <text class="detail-value">{{selectedDateDetail.comment}}</text>
        </view>
      </scroll-view>
      <button class="modal-close-btn" bindtap="closeCheckinDetailModal">关闭</button>
    </view>
  </view>
</view>
