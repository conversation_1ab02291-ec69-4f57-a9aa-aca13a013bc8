{"permissions": {"allow": ["<PERSON><PERSON>(claude mcp:*)", "Bash(set CLAUDE_CODE_GIT_BASH_PATH=D:Gitgit-bash.exe)", "Bash(set CLAUDE_CODE_GIT_BASH_PATH=D:/Git/git-bash.exe)", "Bash(set CLAUDE_CODE_GIT_BASH_PATH=D:Gitbinbash.exe)", "<PERSON><PERSON>(setx:*)", "Bash(npm install:*)", "Bash(npx @modelcontextprotocol/create-server --help)", "Bash(npm search:*)", "WebFetch(domain:github.com)", "WebFetch(domain:dev.to)", "WebFetch(domain:github-trending.today)"], "deny": []}}